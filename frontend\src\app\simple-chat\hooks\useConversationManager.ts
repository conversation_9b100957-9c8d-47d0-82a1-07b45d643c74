import { useConversations } from '@/contexts/ConversationContext';

interface UseConversationManagerReturn {
  conversations: Conversation[];
  currentConversation: Conversation | null;
  loading: boolean;
  error: string | null;
  
  // 操作函数
  loadConversations: () => Promise<void>;
  createConversation: (title: string, model: string) => Promise<number | null>;
  switchConversation: (id: number) => Promise<void>;
  deleteConversation: (id: number) => Promise<void>;
  updateConversationTitle: (id: number, title: string) => Promise<void>;
}

export function useConversationManager(): UseConversationManagerReturn {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 加载对话列表
  const loadConversations = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/conversations');
      const data = await response.json();
      
      if (data.success) {
        setConversations(data.conversations || []);
      } else {
        setError(data.error || '加载对话列表失败');
      }
    } catch (err) {
      setError('网络错误，加载对话列表失败');
      console.error('加载对话列表失败:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 创建新对话
  const createConversation = useCallback(async (title: string, model: string): Promise<number | null> => {
    try {
      setError(null);
      console.log(`[useConversationManager] 开始创建对话: ${title}, 模型: ${model}`);
      
      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ title, model }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        console.log(`[useConversationManager] 对话创建成功:`, data.conversation);
        
        // 刷新对话列表
        await loadConversations();
        console.log(`[useConversationManager] 对话列表已刷新`);
        
        // 切换到新创建的对话
        setCurrentConversation(data.conversation);
        console.log(`[useConversationManager] 当前对话已设置为新创建的对话:`, data.conversation);
        
        return data.conversation.id;
      } else {
        console.error(`[useConversationManager] 创建对话失败:`, data.error);
        setError(data.error || '创建对话失败');
        return null;
      }
    } catch (err) {
      console.error(`[useConversationManager] 创建对话异常:`, err);
      setError('网络错误，创建对话失败');
      return null;
    }
  }, [loadConversations]);

  // 切换对话
  const switchConversation = useCallback(async (id: number) => {
    try {
      setError(null);
      console.log(`[useConversationManager] 开始切换到对话 ${id}`);
      
      const response = await fetch(`/api/conversations/${id}`);
      const data = await response.json();
      
      if (data.success) {
        console.log(`[useConversationManager] 成功获取对话 ${id} 信息:`, data.conversation);
        setCurrentConversation(data.conversation);
        console.log(`[useConversationManager] 当前对话状态已更新为:`, data.conversation);
      } else {
        console.error(`[useConversationManager] 切换对话 ${id} 失败:`, data.error);
        setError(data.error || '切换对话失败');
        throw new Error(data.error || '切换对话失败');
      }
    } catch (err) {
      console.error(`[useConversationManager] 切换对话 ${id} 异常:`, err);
      setError('网络错误，切换对话失败');
      throw err;
    }
  }, []);

  // 删除对话
  const deleteConversation = useCallback(async (id: number) => {
    try {
      setError(null);
      
      if (!confirm('确定要删除这个对话吗？此操作无法撤销。')) {
        return;
      }
      
      const response = await fetch(`/api/conversations/${id}`, {
        method: 'DELETE',
      });
      
      const data = await response.json();
      
      if (data.success) {
        // 如果删除的是当前对话，清空当前对话
        if (currentConversation?.id === id) {
          setCurrentConversation(null);
        }
        // 刷新对话列表
        await loadConversations();
      } else {
        setError(data.error || '删除对话失败');
      }
    } catch (err) {
      setError('网络错误，删除对话失败');
      console.error('删除对话失败:', err);
    }
  }, [currentConversation, loadConversations]);

  // 更新对话标题
  const updateConversationTitle = useCallback(async (id: number, title: string) => {
    try {
      setError(null);
      
      const response = await fetch(`/api/conversations/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ title }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        // 更新本地状态
        setConversations(prev => 
          prev.map(conv => 
            conv.id === id ? { ...conv, title } : conv
          )
        );
        
        // 如果是当前对话，也更新当前对话状态
        if (currentConversation?.id === id) {
          setCurrentConversation(prev => 
            prev ? { ...prev, title } : null
          );
        }
      } else {
        setError(data.error || '更新对话标题失败');
      }
    } catch (err) {
      setError('网络错误，更新对话标题失败');
      console.error('更新对话标题失败:', err);
    }
  }, [currentConversation]);

  // 初始化时加载对话列表
  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  return {
    conversations,
    currentConversation,
    loading,
    error,
    
    loadConversations,
    createConversation,
    switchConversation,
    deleteConversation,
    updateConversationTitle,
  };
} 