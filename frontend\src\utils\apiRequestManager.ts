/**
 * API请求管理器 - 提供请求缓存、去重和防抖功能
 */

interface CacheEntry {
  data: any;
  timestamp: number;
  promise?: Promise<any>;
}

interface RequestConfig {
  cacheKey?: string;
  cacheDuration?: number; // 缓存持续时间（毫秒）
  debounceDelay?: number; // 防抖延迟（毫秒）
  enableCache?: boolean;
  enableDebounce?: boolean;
}

class ApiRequestManager {
  private cache = new Map<string, CacheEntry>();
  private pendingRequests = new Map<string, Promise<any>>();
  private debounceTimers = new Map<string, NodeJS.Timeout>();
  
  private readonly DEFAULT_CACHE_DURATION = 30000; // 30秒
  private readonly DEFAULT_DEBOUNCE_DELAY = 300; // 300毫秒

  /**
   * 执行API请求，支持缓存和去重
   */
  async request<T>(
    url: string,
    options: RequestInit = {},
    config: RequestConfig = {}
  ): Promise<T> {
    const {
      cacheKey = this.generateCacheKey(url, options),
      cacheDuration = this.DEFAULT_CACHE_DURATION,
      debounceDelay = this.DEFAULT_DEBOUNCE_DELAY,
      enableCache = true,
      enableDebounce = false,
    } = config;

    // 如果启用防抖，延迟执行
    if (enableDebounce) {
      return this.debounceRequest(url, options, config);
    }

    // 检查缓存
    if (enableCache && this.isCacheValid(cacheKey, cacheDuration)) {
      console.log(`[ApiRequestManager] 使用缓存: ${cacheKey}`);
      return this.cache.get(cacheKey)!.data;
    }

    // 检查是否有相同的请求正在进行
    if (this.pendingRequests.has(cacheKey)) {
      console.log(`[ApiRequestManager] 复用进行中的请求: ${cacheKey}`);
      return this.pendingRequests.get(cacheKey)!;
    }

    // 执行新请求
    const requestPromise = this.executeRequest<T>(url, options);
    
    // 存储进行中的请求
    this.pendingRequests.set(cacheKey, requestPromise);

    try {
      const result = await requestPromise;
      
      // 缓存结果
      if (enableCache) {
        this.cache.set(cacheKey, {
          data: result,
          timestamp: Date.now(),
        });
      }
      
      console.log(`[ApiRequestManager] 请求完成: ${cacheKey}`);
      return result;
    } catch (error) {
      console.error(`[ApiRequestManager] 请求失败: ${cacheKey}`, error);
      throw error;
    } finally {
      // 清理进行中的请求
      this.pendingRequests.delete(cacheKey);
    }
  }

  /**
   * 防抖请求
   */
  private debounceRequest<T>(
    url: string,
    options: RequestInit,
    config: RequestConfig
  ): Promise<T> {
    const { cacheKey = this.generateCacheKey(url, options), debounceDelay = this.DEFAULT_DEBOUNCE_DELAY } = config;
    
    return new Promise((resolve, reject) => {
      // 清除之前的定时器
      if (this.debounceTimers.has(cacheKey)) {
        clearTimeout(this.debounceTimers.get(cacheKey)!);
      }

      // 设置新的定时器
      const timer = setTimeout(async () => {
        try {
          const result = await this.request<T>(url, options, { ...config, enableDebounce: false });
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          this.debounceTimers.delete(cacheKey);
        }
      }, debounceDelay);

      this.debounceTimers.set(cacheKey, timer);
    });
  }

  /**
   * 执行实际的网络请求
   */
  private async executeRequest<T>(url: string, options: RequestInit): Promise<T> {
    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cacheKey: string, cacheDuration: number): boolean {
    const entry = this.cache.get(cacheKey);
    if (!entry) return false;
    
    const now = Date.now();
    return (now - entry.timestamp) < cacheDuration;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(url: string, options: RequestInit): string {
    const method = options.method || 'GET';
    const body = options.body ? JSON.stringify(options.body) : '';
    return `${method}:${url}:${body}`;
  }

  /**
   * 清除指定缓存
   */
  clearCache(cacheKey?: string): void {
    if (cacheKey) {
      this.cache.delete(cacheKey);
      console.log(`[ApiRequestManager] 清除缓存: ${cacheKey}`);
    } else {
      this.cache.clear();
      console.log(`[ApiRequestManager] 清除所有缓存`);
    }
  }

  /**
   * 清除过期缓存
   */
  clearExpiredCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if ((now - entry.timestamp) > this.DEFAULT_CACHE_DURATION) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 取消所有进行中的请求
   */
  cancelAllRequests(): void {
    // 清除所有防抖定时器
    for (const timer of this.debounceTimers.values()) {
      clearTimeout(timer);
    }
    this.debounceTimers.clear();
    
    // 清除进行中的请求记录
    this.pendingRequests.clear();
    
    console.log(`[ApiRequestManager] 取消所有请求`);
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

// 创建全局实例
export const apiRequestManager = new ApiRequestManager();

// 便捷方法
export const apiRequest = {
  get: <T>(url: string, config?: RequestConfig) => 
    apiRequestManager.request<T>(url, { method: 'GET' }, config),
  
  post: <T>(url: string, data?: any, config?: RequestConfig) => 
    apiRequestManager.request<T>(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: data ? JSON.stringify(data) : undefined,
    }, config),
  
  put: <T>(url: string, data?: any, config?: RequestConfig) => 
    apiRequestManager.request<T>(url, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: data ? JSON.stringify(data) : undefined,
    }, config),
  
  delete: <T>(url: string, config?: RequestConfig) => 
    apiRequestManager.request<T>(url, { method: 'DELETE' }, config),
  
  patch: <T>(url: string, data?: any, config?: RequestConfig) => 
    apiRequestManager.request<T>(url, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: data ? JSON.stringify(data) : undefined,
    }, config),
};

// 定期清理过期缓存
if (typeof window !== 'undefined') {
  setInterval(() => {
    apiRequestManager.clearExpiredCache();
  }, 60000); // 每分钟清理一次
}
