/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CConversationContext.tsx%22%2C%22ids%22%3A%5B%22ConversationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CConversationContext.tsx%22%2C%22ids%22%3A%5B%22ConversationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/examples/NotificationDemo.tsx */ \"(rsc)/./src/components/notification/examples/NotificationDemo.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationContainer.tsx */ \"(rsc)/./src/components/notification/NotificationContainer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationContext.tsx */ \"(rsc)/./src/components/notification/NotificationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationItem.tsx */ \"(rsc)/./src/components/notification/NotificationItem.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationManager.tsx */ \"(rsc)/./src/components/notification/NotificationManager.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ConversationContext.tsx */ \"(rsc)/./src/contexts/ConversationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/theme/components/ColorThemeScript.tsx */ \"(rsc)/./src/theme/components/ColorThemeScript.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/theme/contexts/ThemeContext.tsx */ \"(rsc)/./src/theme/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1phY2slNUMlNUNEZXNrdG9wJTVDJTVDUlAzMF9rdW5hZ2VudCU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1phY2slNUMlNUNEZXNrdG9wJTVDJTVDUlAzMF9rdW5hZ2VudCU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDWmFjayU1QyU1Q0Rlc2t0b3AlNUMlNUNSUDMwX2t1bmFnZW50JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDbm90aWZpY2F0aW9uJTVDJTVDZXhhbXBsZXMlNUMlNUNOb3RpZmljYXRpb25EZW1vLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMk5vdGlmaWNhdGlvbkRlbW8lMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDWmFjayU1QyU1Q0Rlc2t0b3AlNUMlNUNSUDMwX2t1bmFnZW50JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDbm90aWZpY2F0aW9uJTVDJTVDTm90aWZpY2F0aW9uQ29udGFpbmVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNaYWNrJTVDJTVDRGVza3RvcCU1QyU1Q1JQMzBfa3VuYWdlbnQlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNub3RpZmljYXRpb24lNUMlNUNOb3RpZmljYXRpb25Db250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlMkMlMjJOb3RpZmljYXRpb25Qcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNaYWNrJTVDJTVDRGVza3RvcCU1QyU1Q1JQMzBfa3VuYWdlbnQlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNub3RpZmljYXRpb24lNUMlNUNOb3RpZmljYXRpb25JdGVtLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNaYWNrJTVDJTVDRGVza3RvcCU1QyU1Q1JQMzBfa3VuYWdlbnQlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNub3RpZmljYXRpb24lNUMlNUNOb3RpZmljYXRpb25NYW5hZ2VyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlMkMlMjJOb3RpZmljYXRpb25NYW5hZ2VyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1phY2slNUMlNUNEZXNrdG9wJTVDJTVDUlAzMF9rdW5hZ2VudCU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDY29udGV4dHMlNUMlNUNDb252ZXJzYXRpb25Db250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkNvbnZlcnNhdGlvblByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1phY2slNUMlNUNEZXNrdG9wJTVDJTVDUlAzMF9rdW5hZ2VudCU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDdGhlbWUlNUMlNUNjb21wb25lbnRzJTVDJTVDQ29sb3JUaGVtZVNjcmlwdC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDb2xvclRoZW1lU2NyaXB0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1phY2slNUMlNUNEZXNrdG9wJTVDJTVDUlAzMF9rdW5hZ2VudCU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDdGhlbWUlNUMlNUNjb250ZXh0cyU1QyU1Q1RoZW1lQ29udGV4dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUaGVtZVByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrT0FBMkw7QUFDM0w7QUFDQSwwTkFBZ0o7QUFDaEo7QUFDQSxzTkFBOEk7QUFDOUk7QUFDQSxnTkFBMkk7QUFDM0k7QUFDQSxzTkFBOEk7QUFDOUk7QUFDQSx3TEFBd0s7QUFDeEs7QUFDQSxrTUFBMEs7QUFDMUs7QUFDQSxzTEFBaUsiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIk5vdGlmaWNhdGlvbkRlbW9cIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxaYWNrXFxcXERlc2t0b3BcXFxcUlAzMF9rdW5hZ2VudFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxub3RpZmljYXRpb25cXFxcZXhhbXBsZXNcXFxcTm90aWZpY2F0aW9uRGVtby50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFphY2tcXFxcRGVza3RvcFxcXFxSUDMwX2t1bmFnZW50XFxcXGZyb250ZW5kXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXG5vdGlmaWNhdGlvblxcXFxOb3RpZmljYXRpb25Db250YWluZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxaYWNrXFxcXERlc2t0b3BcXFxcUlAzMF9rdW5hZ2VudFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxub3RpZmljYXRpb25cXFxcTm90aWZpY2F0aW9uQ29udGV4dC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFphY2tcXFxcRGVza3RvcFxcXFxSUDMwX2t1bmFnZW50XFxcXGZyb250ZW5kXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXG5vdGlmaWNhdGlvblxcXFxOb3RpZmljYXRpb25JdGVtLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcWmFja1xcXFxEZXNrdG9wXFxcXFJQMzBfa3VuYWdlbnRcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcbm90aWZpY2F0aW9uXFxcXE5vdGlmaWNhdGlvbk1hbmFnZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDb252ZXJzYXRpb25Qcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFphY2tcXFxcRGVza3RvcFxcXFxSUDMwX2t1bmFnZW50XFxcXGZyb250ZW5kXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxDb252ZXJzYXRpb25Db250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQ29sb3JUaGVtZVNjcmlwdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFphY2tcXFxcRGVza3RvcFxcXFxSUDMwX2t1bmFnZW50XFxcXGZyb250ZW5kXFxcXHNyY1xcXFx0aGVtZVxcXFxjb21wb25lbnRzXFxcXENvbG9yVGhlbWVTY3JpcHQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUaGVtZVByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcWmFja1xcXFxEZXNrdG9wXFxcXFJQMzBfa3VuYWdlbnRcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXHRoZW1lXFxcXGNvbnRleHRzXFxcXFRoZW1lQ29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CConversationContext.tsx%22%2C%22ids%22%3A%5B%22ConversationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1phY2slNUMlNUNEZXNrdG9wJTVDJTVDUlAzMF9rdW5hZ2VudCU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUEwRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcWmFja1xcXFxEZXNrdG9wXFxcXFJQMzBfa3VuYWdlbnRcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"378dbfee842a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzNzhkYmZlZTg0MmFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _theme_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/theme/contexts/ThemeContext */ \"(rsc)/./src/theme/contexts/ThemeContext.tsx\");\n/* harmony import */ var _theme_components_ThemeScript__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/theme/components/ThemeScript */ \"(rsc)/./src/theme/components/ThemeScript.tsx\");\n/* harmony import */ var _theme_components_ColorThemeScript__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/theme/components/ColorThemeScript */ \"(rsc)/./src/theme/components/ColorThemeScript.tsx\");\n/* harmony import */ var _components_notification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/notification */ \"(rsc)/./src/components/notification/index.ts\");\n/* harmony import */ var _components_notification_NotificationManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/notification/NotificationManager */ \"(rsc)/./src/components/notification/NotificationManager.tsx\");\n/* harmony import */ var _contexts_ConversationContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ConversationContext */ \"(rsc)/./src/contexts/ConversationContext.tsx\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: 'Kun Agent',\n    description: '智能对话助手'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_components_ThemeScript__WEBPACK_IMPORTED_MODULE_3__.ThemeScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_components_ColorThemeScript__WEBPACK_IMPORTED_MODULE_4__.ColorThemeScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default().className)} bg-theme-background text-theme-foreground transition-opacity duration-200`,\n                suppressHydrationWarning: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notification__WEBPACK_IMPORTED_MODULE_5__.NotificationProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ConversationContext__WEBPACK_IMPORTED_MODULE_7__.ConversationProvider, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"min-h-screen\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notification_NotificationManager__WEBPACK_IMPORTED_MODULE_6__.NotificationManager, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/notification/NotificationContainer.tsx":
/*!***************************************************************!*\
  !*** ./src/components/notification/NotificationContainer.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationContainer: () => (/* binding */ NotificationContainer)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const NotificationContainer = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotificationContainer() from the server but NotificationContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationContainer.tsx",
"NotificationContainer",
);

/***/ }),

/***/ "(rsc)/./src/components/notification/NotificationContext.tsx":
/*!*************************************************************!*\
  !*** ./src/components/notification/NotificationContext.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),
/* harmony export */   useNotification: () => (/* binding */ useNotification)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const NotificationProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotificationProvider() from the server but NotificationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationContext.tsx",
"NotificationProvider",
);const useNotification = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useNotification() from the server but useNotification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationContext.tsx",
"useNotification",
);

/***/ }),

/***/ "(rsc)/./src/components/notification/NotificationItem.tsx":
/*!**********************************************************!*\
  !*** ./src/components/notification/NotificationItem.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationItem: () => (/* binding */ NotificationItem)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const NotificationItem = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotificationItem() from the server but NotificationItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationItem.tsx",
"NotificationItem",
);

/***/ }),

/***/ "(rsc)/./src/components/notification/NotificationManager.tsx":
/*!*************************************************************!*\
  !*** ./src/components/notification/NotificationManager.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationManager: () => (/* binding */ NotificationManager)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const NotificationManager = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotificationManager() from the server but NotificationManager is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationManager.tsx",
"NotificationManager",
);

/***/ }),

/***/ "(rsc)/./src/components/notification/examples/NotificationDemo.tsx":
/*!*******************************************************************!*\
  !*** ./src/components/notification/examples/NotificationDemo.tsx ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationDemo: () => (/* binding */ NotificationDemo)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const NotificationDemo = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotificationDemo() from the server but NotificationDemo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\examples\\NotificationDemo.tsx",
"NotificationDemo",
);

/***/ }),

/***/ "(rsc)/./src/components/notification/index.ts":
/*!**********************************************!*\
  !*** ./src/components/notification/index.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationContainer: () => (/* reexport safe */ _NotificationContainer__WEBPACK_IMPORTED_MODULE_2__.NotificationContainer),\n/* harmony export */   NotificationDemo: () => (/* reexport safe */ _examples_NotificationDemo__WEBPACK_IMPORTED_MODULE_5__.NotificationDemo),\n/* harmony export */   NotificationItem: () => (/* reexport safe */ _NotificationItem__WEBPACK_IMPORTED_MODULE_3__.NotificationItem),\n/* harmony export */   NotificationManager: () => (/* reexport safe */ _NotificationManager__WEBPACK_IMPORTED_MODULE_4__.NotificationManager),\n/* harmony export */   NotificationProvider: () => (/* reexport safe */ _NotificationContext__WEBPACK_IMPORTED_MODULE_1__.NotificationProvider),\n/* harmony export */   \"default\": () => (/* reexport safe */ _NotificationContext__WEBPACK_IMPORTED_MODULE_1__.NotificationProvider),\n/* harmony export */   useNotification: () => (/* reexport safe */ _NotificationContext__WEBPACK_IMPORTED_MODULE_1__.useNotification)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(rsc)/./src/components/notification/types.ts\");\n/* harmony import */ var _NotificationContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NotificationContext */ \"(rsc)/./src/components/notification/NotificationContext.tsx\");\n/* harmony import */ var _NotificationContainer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NotificationContainer */ \"(rsc)/./src/components/notification/NotificationContainer.tsx\");\n/* harmony import */ var _NotificationItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NotificationItem */ \"(rsc)/./src/components/notification/NotificationItem.tsx\");\n/* harmony import */ var _NotificationManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NotificationManager */ \"(rsc)/./src/components/notification/NotificationManager.tsx\");\n/* harmony import */ var _examples_NotificationDemo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./examples/NotificationDemo */ \"(rsc)/./src/components/notification/examples/NotificationDemo.tsx\");\n\n\n\n\n\n// 演示组件\n\n// 默认导出一个组合组件\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9ub3RpZmljYXRpb24vaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUF3QjtBQUNjO0FBQ0U7QUFDTDtBQUN5QjtBQUU1RCxPQUFPO0FBQ3dEO0FBRS9ELGFBQWE7QUFDMkQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcbm90aWZpY2F0aW9uXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL3R5cGVzJztcclxuZXhwb3J0ICogZnJvbSAnLi9Ob3RpZmljYXRpb25Db250ZXh0JztcclxuZXhwb3J0ICogZnJvbSAnLi9Ob3RpZmljYXRpb25Db250YWluZXInO1xyXG5leHBvcnQgKiBmcm9tICcuL05vdGlmaWNhdGlvbkl0ZW0nO1xyXG5leHBvcnQgeyBOb3RpZmljYXRpb25NYW5hZ2VyIH0gZnJvbSAnLi9Ob3RpZmljYXRpb25NYW5hZ2VyJztcclxuXHJcbi8vIOa8lOekuue7hOS7tlxyXG5leHBvcnQgeyBOb3RpZmljYXRpb25EZW1vIH0gZnJvbSAnLi9leGFtcGxlcy9Ob3RpZmljYXRpb25EZW1vJztcclxuXHJcbi8vIOm7mOiupOWvvOWHuuS4gOS4que7hOWQiOe7hOS7tlxyXG5leHBvcnQgeyBOb3RpZmljYXRpb25Qcm92aWRlciBhcyBkZWZhdWx0IH0gZnJvbSAnLi9Ob3RpZmljYXRpb25Db250ZXh0JzsgIl0sIm5hbWVzIjpbIk5vdGlmaWNhdGlvbk1hbmFnZXIiLCJOb3RpZmljYXRpb25EZW1vIiwiTm90aWZpY2F0aW9uUHJvdmlkZXIiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/notification/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/components/notification/types.ts":
/*!**********************************************!*\
  !*** ./src/components/notification/types.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/notification/types.ts\n");

/***/ }),

/***/ "(rsc)/./src/contexts/ConversationContext.tsx":
/*!**********************************************!*\
  !*** ./src/contexts/ConversationContext.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConversationProvider: () => (/* binding */ ConversationProvider),
/* harmony export */   useConversations: () => (/* binding */ useConversations)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ConversationProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ConversationProvider() from the server but ConversationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\contexts\\ConversationContext.tsx",
"ConversationProvider",
);const useConversations = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useConversations() from the server but useConversations is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\contexts\\ConversationContext.tsx",
"useConversations",
);

/***/ }),

/***/ "(rsc)/./src/theme/components/ColorThemeScript.tsx":
/*!***************************************************!*\
  !*** ./src/theme/components/ColorThemeScript.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ColorThemeScript: () => (/* binding */ ColorThemeScript)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ColorThemeScript = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ColorThemeScript() from the server but ColorThemeScript is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\components\\ColorThemeScript.tsx",
"ColorThemeScript",
);

/***/ }),

/***/ "(rsc)/./src/theme/components/ThemeScript.tsx":
/*!**********************************************!*\
  !*** ./src/theme/components/ThemeScript.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeScript: () => (/* binding */ ThemeScript)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n// 主题预加载脚本组件\n// 这个组件会在页面加载前立即执行，防止主题闪烁\n\nfunction ThemeScript() {\n    const initScript = `\n    (function() {\n      try {\n        // 1. 主题处理 - 优先从 localStorage 获取主题\n        const savedTheme = localStorage.getItem('theme');\n        \n        // 如果没有保存的主题，则根据系统偏好设置\n        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n        \n        // 决定最终主题\n        const theme = savedTheme || systemTheme;\n        \n        // 立即在 <html> 元素上应用主题类\n        document.documentElement.classList.add(theme);\n\n        // 如果没有保存过主题，则将当前主题存入 localStorage\n        if (!savedTheme) {\n          localStorage.setItem('theme', theme);\n        }\n\n        // 2. 侧边栏状态处理 - 立即设置状态避免闪烁\n        const sidebarExpanded = localStorage.getItem('sidebar-expanded');\n        const isExpanded = sidebarExpanded !== null ? JSON.parse(sidebarExpanded) : true;\n        \n        // 立即设置侧边栏状态，避免任何延迟\n        document.documentElement.setAttribute('data-sidebar-state', isExpanded ? 'expanded' : 'collapsed');\n        \n      } catch (e) {\n        // 如果出现错误，默认使用浅色主题和展开的侧边栏\n        document.documentElement.classList.add('light');\n        document.documentElement.setAttribute('data-sidebar-state', 'expanded');\n      }\n    })();\n  `;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        dangerouslySetInnerHTML: {\n            __html: initScript\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ThemeScript.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/theme/components/ThemeScript.tsx\n");

/***/ }),

/***/ "(rsc)/./src/theme/contexts/ThemeContext.tsx":
/*!*********************************************!*\
  !*** ./src/theme/contexts/ThemeContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme),
/* harmony export */   useThemeToggle: () => (/* binding */ useThemeToggle)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\contexts\\ThemeContext.tsx",
"ThemeProvider",
);const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\contexts\\ThemeContext.tsx",
"useTheme",
);const useThemeToggle = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useThemeToggle() from the server but useThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\contexts\\ThemeContext.tsx",
"useThemeToggle",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CConversationContext.tsx%22%2C%22ids%22%3A%5B%22ConversationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CConversationContext.tsx%22%2C%22ids%22%3A%5B%22ConversationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/examples/NotificationDemo.tsx */ \"(ssr)/./src/components/notification/examples/NotificationDemo.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationContainer.tsx */ \"(ssr)/./src/components/notification/NotificationContainer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationContext.tsx */ \"(ssr)/./src/components/notification/NotificationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationItem.tsx */ \"(ssr)/./src/components/notification/NotificationItem.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationManager.tsx */ \"(ssr)/./src/components/notification/NotificationManager.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ConversationContext.tsx */ \"(ssr)/./src/contexts/ConversationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/theme/components/ColorThemeScript.tsx */ \"(ssr)/./src/theme/components/ColorThemeScript.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/theme/contexts/ThemeContext.tsx */ \"(ssr)/./src/theme/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CConversationContext.tsx%22%2C%22ids%22%3A%5B%22ConversationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1phY2slNUMlNUNEZXNrdG9wJTVDJTVDUlAzMF9rdW5hZ2VudCU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUEwRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcWmFja1xcXFxEZXNrdG9wXFxcXFJQMzBfa3VuYWdlbnRcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/Sidebar.tsx":
/*!*****************************!*\
  !*** ./src/app/Sidebar.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/panel-left.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/panel-right.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/workflow.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _theme_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/theme/components/ThemeToggle */ \"(ssr)/./src/theme/components/ThemeToggle.tsx\");\n/* harmony import */ var _theme_components_ColorThemeSwitcher__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/theme/components/ColorThemeSwitcher */ \"(ssr)/./src/theme/components/ColorThemeSwitcher.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_ConversationContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/ConversationContext */ \"(ssr)/./src/contexts/ConversationContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\n\n// 简化的侧边栏状态管理\nfunction useSidebarState() {\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const toggleSidebar = ()=>{\n        const newState = !isExpanded;\n        setIsExpanded(newState);\n        if (false) {}\n    };\n    // 在客户端挂载后同步真实状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useSidebarState.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"useSidebarState.useEffect\"], [\n        isExpanded\n    ]);\n    return {\n        isExpanded,\n        toggleSidebar\n    };\n}\nfunction Sidebar({ onCreateConversation, onLoadConversation, onDeleteConversation }) {\n    const { isExpanded, toggleSidebar } = useSidebarState();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    // 使用全局对话状态\n    const { conversations, currentConversation } = (0,_contexts_ConversationContext__WEBPACK_IMPORTED_MODULE_6__.useConversations)();\n    // 处理创建新对话的逻辑\n    const handleNewConversation = ()=>{\n        if (pathname === '/simple-chat') {\n            onCreateConversation();\n        } else {\n            router.push('/simple-chat?new=true');\n        }\n    };\n    // 处理进入聊天页面的逻辑\n    const handleGoToChat = ()=>{\n        const hasConversations = conversations && conversations.length > 0;\n        if (pathname === '/simple-chat') {\n            if (hasConversations && !currentConversation) {\n                const latestConversation = conversations.sort((a, b)=>new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())[0];\n                onLoadConversation(latestConversation.id);\n            } else if (!hasConversations) {\n                onCreateConversation();\n            }\n        } else {\n            if (hasConversations) {\n                const latestConversation = conversations.sort((a, b)=>new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())[0];\n                router.push(`/simple-chat?id=${latestConversation.id}`);\n            } else {\n                router.push('/simple-chat?new=true');\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sidebar-container relative bg-theme-card border-r border-theme-border flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"group p-4 border-b border-theme-border flex items-center justify-center relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/\",\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"/assets/<EMAIL>\",\n                                alt: \"Kun Agent Logo\",\n                                className: \"w-8 h-8 transition-all duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"sidebar-text text-lg font-bold text-theme-foreground ml-2\",\n                                children: \"Kun Agent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleSidebar,\n                        className: \"p-1.5 rounded-full text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-card-hover transition-all duration-300 opacity-0 group-hover:opacity-100 absolute right-0 translate-x-1/2 top-1/2 -translate-y-1/2 bg-theme-card border border-theme-border shadow-sm\",\n                        children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleNewConversation,\n                    className: \"sidebar-button group relative w-full flex items-center gap-3 p-3 rounded-lg bg-theme-primary text-white hover:bg-theme-primary-hover transition-colors duration-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-5 h-5 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sidebar-text text-sm font-semibold\",\n                            children: \"新建对话\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                            children: \"新建对话\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pl-2 flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/\",\n                            className: `sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 ${pathname === '/' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"首页\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"首页\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/mcp-config\",\n                            className: `sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 ${pathname === '/mcp-config' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"MCP配置\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"MCP配置\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/model-manager\",\n                            className: `sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 ${pathname === '/model-manager' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"模型管理\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"模型管理\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/agents\",\n                            className: `sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 ${pathname === '/agents' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"智能体管理\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"智能体管理\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleGoToChat,\n                            className: `sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 w-full text-left ${pathname === '/simple-chat' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"AI聊天\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: conversations.length > 0 ? \"进入最新对话\" : \"开始AI聊天\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-theme-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/conversations\",\n                                className: `sidebar-button sidebar-nav-item group relative w-full flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 ${pathname === '/conversations' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-card-hover'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sidebar-icon-container\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-5 h-5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sidebar-text text-sm\",\n                                        children: \"对话历史\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                        children: \"对话历史\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/settings\",\n                                className: `sidebar-button sidebar-nav-item group relative w-full flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 ${pathname === '/settings' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-card-hover'}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sidebar-icon-container\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-5 h-5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sidebar-text text-sm\",\n                                        children: \"设置\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                        children: \"设置\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 pb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sidebar-button group relative w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 hover:bg-theme-card-hover\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container theme-toggle-icon\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__.ThemeToggle, {\n                                            variant: \"icon\",\n                                            size: \"sm\",\n                                            className: \"sidebar-collapsed-only\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this),\n                                        isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_components_ColorThemeSwitcher__WEBPACK_IMPORTED_MODULE_3__.ColorThemeSwitcher, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 30\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm text-theme-foreground-muted flex-1\",\n                                    children: \"外观主题\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-text\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__.ThemeToggle, {\n                                        variant: \"icon\",\n                                        size: \"sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"切换主题\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/app/Sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Loading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Loading */ \"(ssr)/./src/components/Loading.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// 本地存储键名 - 与聊天页面保持一致\nconst SELECTED_MODEL_KEY = 'chat_selected_model';\nfunction HomePage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 从本地存储加载已保存的模型选择\n    const loadSavedModel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[loadSavedModel]\": ()=>{\n            try {\n                const savedModel = localStorage.getItem(SELECTED_MODEL_KEY);\n                return savedModel;\n            } catch (error) {\n                console.warn('无法从localStorage读取保存的模型:', error);\n                return null;\n            }\n        }\n    }[\"HomePage.useCallback[loadSavedModel]\"], []);\n    // 保存模型选择到本地存储\n    const saveModelSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[saveModelSelection]\": (modelName)=>{\n            try {\n                localStorage.setItem(SELECTED_MODEL_KEY, modelName);\n            } catch (error) {\n                console.warn('无法保存模型选择到localStorage:', error);\n            }\n        }\n    }[\"HomePage.useCallback[saveModelSelection]\"], []);\n    // 包装模型选择函数以添加持久化\n    const handleModelChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[handleModelChange]\": (modelName)=>{\n            setSelectedModel(modelName);\n            saveModelSelection(modelName);\n        }\n    }[\"HomePage.useCallback[handleModelChange]\"], [\n        saveModelSelection\n    ]);\n    // 获取模型列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchModels = {\n                \"HomePage.useEffect.fetchModels\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const response = await fetch('/api/models');\n                        if (response.ok) {\n                            const data = await response.json();\n                            setModels(data.models || []);\n                            const savedModel = loadSavedModel();\n                            if (data.models && data.models.length > 0) {\n                                // 检查保存的模型是否仍然可用\n                                const isModelAvailable = savedModel && data.models.some({\n                                    \"HomePage.useEffect.fetchModels\": (model)=>model.name === savedModel\n                                }[\"HomePage.useEffect.fetchModels\"]);\n                                if (isModelAvailable) {\n                                    // 使用保存的模型\n                                    setSelectedModel(savedModel);\n                                } else {\n                                    // 使用第一个可用模型并保存\n                                    const firstModel = data.models[0].name;\n                                    setSelectedModel(firstModel);\n                                    saveModelSelection(firstModel);\n                                }\n                            }\n                        } else {\n                            setError('获取模型列表失败');\n                        }\n                    } catch (err) {\n                        console.error('获取模型失败:', err);\n                        setError('获取模型列表失败，请确保Ollama正在运行');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HomePage.useEffect.fetchModels\"];\n            fetchModels();\n        }\n    }[\"HomePage.useEffect\"], [\n        loadSavedModel,\n        saveModelSelection\n    ]);\n    // 创建新对话并跳转到聊天页面\n    const handleCreateConversation = async ()=>{\n        if (!selectedModel) {\n            setError('请选择一个模型');\n            return;\n        }\n        try {\n            // 创建新对话\n            const title = `新对话 - ${new Date().toLocaleString('zh-CN', {\n                month: 'short',\n                day: 'numeric',\n                hour: '2-digit',\n                minute: '2-digit'\n            })}`;\n            const response = await fetch('/api/conversations', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    title,\n                    model: selectedModel\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.conversation?.id) {\n                    // 跳转到聊天页面，包含新对话的ID\n                    router.push(`/simple-chat?id=${data.conversation.id}`);\n                } else {\n                    setError('创建对话失败');\n                }\n            } else {\n                setError('创建对话失败');\n            }\n        } catch (err) {\n            console.error('创建对话失败:', err);\n            setError('创建对话失败');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-theme-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                onCreateConversation: handleCreateConversation,\n                onLoadConversation: ()=>{},\n                onDeleteConversation: ()=>{}\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loading__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: \"normal\",\n                        text: \"正在加载模型列表...\",\n                        showText: true,\n                        containerStyle: {\n                            padding: '3rem'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this) : // 欢迎页面内容 - 原WelcomePage组件的内容\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-16 h-16 text-theme-foreground-muted mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-theme-foreground mb-2\",\n                                children: \"Kun Agent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-theme-foreground-muted mb-6\",\n                                children: \"智能对话助手，选择一个模型开始您的AI聊天体验\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-xs mx-auto mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedModel,\n                                    onChange: (e)=>handleModelChange(e.target.value),\n                                    className: \"w-full p-2 text-sm border border-theme-input-border rounded-md bg-theme-input text-theme-foreground focus:border-theme-input-focus focus:ring-1 focus:ring-theme-input-focus transition-colors duration-200\",\n                                    \"aria-label\": \"选择模型\",\n                                    children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: model.name,\n                                            children: model.name\n                                        }, model.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-3 bg-red-100 dark:bg-theme-error/10 border border-red-300 dark:border-theme-error/40 rounded-lg text-red-700 dark:text-red-300 text-sm max-w-md mx-auto\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 17\n                            }, this),\n                            models.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-theme-foreground-muted\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"暂无可用模型\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-2\",\n                                        children: \"请确保 Ollama 正在运行并已安装模型\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCreateConversation,\n                                disabled: !selectedModel,\n                                className: \"px-8 py-4 bg-theme-primary text-white rounded-lg hover:bg-theme-primary-hover disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 mx-auto transition-colors duration-200 text-lg font-medium\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"开始聊天\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Loading.tsx":
/*!************************************!*\
  !*** ./src/components/Loading.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InlineLoading: () => (/* binding */ InlineLoading),\n/* harmony export */   LoadingLarge: () => (/* binding */ LoadingLarge),\n/* harmony export */   LoadingSmall: () => (/* binding */ LoadingSmall),\n/* harmony export */   ModalLoading: () => (/* binding */ ModalLoading),\n/* harmony export */   PageLoading: () => (/* binding */ PageLoading),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LoadingSmall,LoadingLarge,PageLoading,ModalLoading,InlineLoading,default auto */ \n\nconst Loading = ({ size = 'normal', text = '正在加载...', showText = true, className = '', containerStyle, textStyle })=>{\n    // 根据尺寸确定spinner类名和容器样式\n    const getSpinnerClass = ()=>{\n        switch(size){\n            case 'small':\n                return 'spinner-small';\n            case 'large':\n                return 'spinner';\n            case 'normal':\n            default:\n                return 'spinner';\n        }\n    };\n    const getContainerPadding = ()=>{\n        switch(size){\n            case 'small':\n                return '1rem';\n            case 'large':\n                return '3rem';\n            case 'normal':\n            default:\n                return '2rem';\n        }\n    };\n    const getGap = ()=>{\n        switch(size){\n            case 'small':\n                return '0.75rem';\n            case 'large':\n                return '1.5rem';\n            case 'normal':\n            default:\n                return '1rem';\n        }\n    };\n    const getTextSize = ()=>{\n        switch(size){\n            case 'small':\n                return '0.875rem'; // text-sm\n            case 'large':\n                return '1.125rem'; // text-lg\n            case 'normal':\n            default:\n                return '1rem'; // text-base\n        }\n    };\n    const defaultContainerStyle = {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: getContainerPadding(),\n        gap: getGap(),\n        ...containerStyle\n    };\n    const defaultTextStyle = {\n        fontSize: getTextSize(),\n        fontWeight: '500',\n        color: 'var(--color-foreground-muted)',\n        textAlign: 'center',\n        ...textStyle\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `loading-container ${className}`,\n        style: defaultContainerStyle,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: getSpinnerClass(),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined),\n            showText && text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                style: defaultTextStyle,\n                children: text\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n// 预设的Loading组件变体\nconst LoadingSmall = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loading, {\n        ...props,\n        size: \"small\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n        lineNumber: 122,\n        columnNumber: 3\n    }, undefined);\nconst LoadingLarge = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loading, {\n        ...props,\n        size: \"large\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n        lineNumber: 126,\n        columnNumber: 3\n    }, undefined);\n// 页面级Loading组件\nconst PageLoading = ({ text = '正在加载页面...', fullScreen = true })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: fullScreen ? 'min-h-screen' : 'min-h-[400px]',\n        style: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            backgroundColor: 'var(--color-background-secondary)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loading, {\n            size: \"normal\",\n            text: text,\n            showText: true,\n            containerStyle: {\n                padding: '3rem'\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n            lineNumber: 146,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n        lineNumber: 137,\n        columnNumber: 3\n    }, undefined);\n// 模态框Loading组件\nconst ModalLoading = ({ text = '处理中...' })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"rounded-2xl p-8 shadow-2xl\",\n            style: {\n                backgroundColor: 'var(--color-card)',\n                border: '1px solid var(--color-border)',\n                background: `linear-gradient(135deg, \n          var(--color-card) 0%, \n          var(--color-background-secondary) 100%)`\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loading, {\n                size: \"normal\",\n                text: text,\n                showText: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n            lineNumber: 164,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n        lineNumber: 161,\n        columnNumber: 3\n    }, undefined);\n// 内联Loading组件\nconst InlineLoading = ({ text = '加载中...', size = 'small' })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: 'inline-flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loading, {\n            size: size,\n            text: text,\n            showText: !!text,\n            containerStyle: {\n                padding: '0',\n                gap: '0.5rem'\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n            lineNumber: 195,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\Loading.tsx\",\n        lineNumber: 188,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Loading);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Loading.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/notification/NotificationContainer.tsx":
/*!***************************************************************!*\
  !*** ./src/components/notification/NotificationContainer.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationContainer: () => (/* binding */ NotificationContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _NotificationItem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NotificationItem */ \"(ssr)/./src/components/notification/NotificationItem.tsx\");\n/* __next_internal_client_entry_do_not_use__ NotificationContainer auto */ \n\n\n\nfunction NotificationContainer({ notifications, onDismiss, position = 'top-right', maxNotifications = 5 }) {\n    // 限制显示的通知数量\n    const visibleNotifications = notifications.slice(0, maxNotifications);\n    // 根据位置获取容器样式\n    const getContainerStyles = ()=>{\n        const baseStyles = 'fixed z-50 flex flex-col gap-3 p-4 pointer-events-none';\n        const positionStyles = {\n            'top-right': 'top-0 right-0',\n            'top-left': 'top-0 left-0',\n            'bottom-right': 'bottom-0 right-0',\n            'bottom-left': 'bottom-0 left-0'\n        };\n        return `${baseStyles} ${positionStyles[position]}`;\n    };\n    // 根据位置决定通知堆叠顺序\n    const orderedNotifications = position.includes('bottom') ? [\n        ...visibleNotifications\n    ].reverse() : visibleNotifications;\n    // 居中弹窗样式\n    const getCenterModalStyles = ()=>'fixed z-[9999] left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center pointer-events-auto';\n    if (notifications.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: notifications.filter((n)=>n.actions && n.actions.length > 0).map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: getCenterModalStyles(),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationItem__WEBPACK_IMPORTED_MODULE_2__.NotificationItem, {\n                            notification: notification,\n                            onDismiss: onDismiss,\n                            position: position\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this)\n                    }, notification.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: getContainerStyles(),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                        mode: \"popLayout\",\n                        children: orderedNotifications.filter((n)=>!(n.actions && n.actions.length > 0)).map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pointer-events-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationItem__WEBPACK_IMPORTED_MODULE_2__.NotificationItem, {\n                                    notification: notification,\n                                    onDismiss: onDismiss,\n                                    position: position\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            }, notification.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    notifications.length > maxNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pointer-events-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-theme-background-secondary border border-theme-border rounded-xl p-3 shadow-lg backdrop-blur-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-theme-foreground-muted text-center\",\n                                    children: [\n                                        \"还有 \",\n                                        notifications.length - maxNotifications,\n                                        \" 条通知...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        // 清除所有超出显示限制的通知\n                                        notifications.slice(maxNotifications).forEach((notification)=>{\n                                            onDismiss(notification.id);\n                                        });\n                                    },\n                                    className: \"w-full mt-2 px-3 py-1.5 text-xs font-medium text-theme-foreground-muted hover:text-theme-foreground bg-theme-background-tertiary hover:bg-theme-card-hover rounded-lg transition-colors\",\n                                    children: \"清除所有\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContainer.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/notification/NotificationContainer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/notification/NotificationContext.tsx":
/*!*************************************************************!*\
  !*** ./src/components/notification/NotificationContext.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   useNotification: () => (/* binding */ useNotification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ NotificationProvider,useNotification auto */ \n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction NotificationProvider({ children, maxNotifications = 10, defaultDuration = 4000 }) {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 生成唯一ID\n    const generateId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[generateId]\": ()=>{\n            return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n        }\n    }[\"NotificationProvider.useCallback[generateId]\"], []);\n    // 添加通知\n    const show = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[show]\": (config)=>{\n            const id = generateId();\n            const notification = {\n                id,\n                type: config.type,\n                title: config.title,\n                message: config.message,\n                duration: config.duration ?? defaultDuration,\n                dismissible: config.dismissible ?? true,\n                actions: config.actions,\n                icon: config.icon,\n                timestamp: Date.now()\n            };\n            setNotifications({\n                \"NotificationProvider.useCallback[show]\": (prev)=>{\n                    const newNotifications = [\n                        notification,\n                        ...prev\n                    ];\n                    // 限制通知数量，移除最旧的通知\n                    return newNotifications.slice(0, maxNotifications);\n                }\n            }[\"NotificationProvider.useCallback[show]\"]);\n            return id;\n        }\n    }[\"NotificationProvider.useCallback[show]\"], [\n        generateId,\n        defaultDuration,\n        maxNotifications\n    ]);\n    // 便捷方法\n    const success = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[success]\": (title, message, options)=>{\n            return show({\n                type: 'success',\n                title,\n                message,\n                ...options\n            });\n        }\n    }[\"NotificationProvider.useCallback[success]\"], [\n        show\n    ]);\n    const error = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[error]\": (title, message, options)=>{\n            return show({\n                type: 'error',\n                title,\n                message,\n                duration: options?.duration ?? 6000,\n                ...options\n            });\n        }\n    }[\"NotificationProvider.useCallback[error]\"], [\n        show\n    ]);\n    const warning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[warning]\": (title, message, options)=>{\n            return show({\n                type: 'warning',\n                title,\n                message,\n                ...options\n            });\n        }\n    }[\"NotificationProvider.useCallback[warning]\"], [\n        show\n    ]);\n    const info = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[info]\": (title, message, options)=>{\n            return show({\n                type: 'info',\n                title,\n                message,\n                ...options\n            });\n        }\n    }[\"NotificationProvider.useCallback[info]\"], [\n        show\n    ]);\n    // 移除指定通知\n    const dismiss = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[dismiss]\": (id)=>{\n            setNotifications({\n                \"NotificationProvider.useCallback[dismiss]\": (prev)=>prev.filter({\n                        \"NotificationProvider.useCallback[dismiss]\": (notification)=>notification.id !== id\n                    }[\"NotificationProvider.useCallback[dismiss]\"])\n            }[\"NotificationProvider.useCallback[dismiss]\"]);\n        }\n    }[\"NotificationProvider.useCallback[dismiss]\"], []);\n    // 清除所有通知\n    const dismissAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationProvider.useCallback[dismissAll]\": ()=>{\n            setNotifications([]);\n        }\n    }[\"NotificationProvider.useCallback[dismissAll]\"], []);\n    const value = {\n        notifications,\n        show,\n        success,\n        error,\n        warning,\n        info,\n        dismiss,\n        dismissAll\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationContext.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n// Hook for using notifications\nfunction useNotification() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n    if (!context) {\n        throw new Error('useNotification must be used within a NotificationProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/notification/NotificationContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/notification/NotificationItem.tsx":
/*!**********************************************************!*\
  !*** ./src/components/notification/NotificationItem.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationItem: () => (/* binding */ NotificationItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ NotificationItem auto */ \n\n\n\nfunction NotificationItem({ notification, onDismiss, position }) {\n    // 自动消失逻辑\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationItem.useEffect\": ()=>{\n            if (notification.duration && notification.duration > 0) {\n                const timer = setTimeout({\n                    \"NotificationItem.useEffect.timer\": ()=>{\n                        onDismiss(notification.id);\n                    }\n                }[\"NotificationItem.useEffect.timer\"], notification.duration);\n                return ({\n                    \"NotificationItem.useEffect\": ()=>clearTimeout(timer)\n                })[\"NotificationItem.useEffect\"];\n            }\n        }\n    }[\"NotificationItem.useEffect\"], [\n        notification.duration,\n        notification.id,\n        onDismiss\n    ]);\n    // 图标映射\n    const getIcon = (type)=>{\n        const defaultIcons = {\n            success: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            error: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            warning: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            info: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        };\n        if (notification.icon) {\n            return notification.icon;\n        }\n        const IconComponent = defaultIcons[type];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n            className: \"w-5 h-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n            lineNumber: 50,\n            columnNumber: 12\n        }, this);\n    };\n    // 样式配置\n    const getStyles = (type)=>{\n        const styles = {\n            success: {\n                container: 'bg-[var(--color-background-secondary)] border border-[var(--color-success)]',\n                icon: 'text-[var(--color-success)]',\n                title: 'text-[var(--color-foreground)]',\n                message: 'text-[var(--color-foreground-muted)]'\n            },\n            error: {\n                container: 'bg-[var(--color-background-secondary)] border border-[var(--color-error)]',\n                icon: 'text-[var(--color-error)]',\n                title: 'text-[var(--color-foreground)]',\n                message: 'text-[var(--color-foreground-muted)]'\n            },\n            warning: {\n                container: 'bg-[var(--color-background-secondary)] border border-[var(--color-warning)]',\n                icon: 'text-[var(--color-warning)]',\n                title: 'text-[var(--color-foreground)]',\n                message: 'text-[var(--color-foreground-muted)]'\n            },\n            info: {\n                container: 'bg-[var(--color-background-secondary)] border border-[var(--color-info)]',\n                icon: 'text-[var(--color-info)]',\n                title: 'text-[var(--color-foreground)]',\n                message: 'text-[var(--color-foreground-muted)]'\n            }\n        };\n        return styles[type];\n    };\n    // 动画变体\n    const getAnimationVariants = ()=>{\n        const isRight = position.includes('right');\n        const isTop = position.includes('top');\n        return {\n            initial: {\n                opacity: 0,\n                x: isRight ? 300 : -300,\n                y: isTop ? -20 : 20,\n                scale: 0.9\n            },\n            animate: {\n                opacity: 1,\n                x: 0,\n                y: 0,\n                scale: 1\n            },\n            exit: {\n                opacity: 0,\n                x: isRight ? 300 : -300,\n                scale: 0.9,\n                transition: {\n                    duration: 0.2\n                }\n            }\n        };\n    };\n    const styles = getStyles(notification.type);\n    const icon = getIcon(notification.type);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n        layout: true,\n        variants: getAnimationVariants(),\n        initial: \"initial\",\n        animate: \"animate\",\n        exit: \"exit\",\n        transition: {\n            type: \"spring\",\n            stiffness: 300,\n            damping: 30\n        },\n        className: \"w-full max-w-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `\n        relative p-4 rounded-lg border shadow-sm\n        transition-all duration-200\n        ${styles.container}\n      `,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex-shrink-0 ${styles.icon}`,\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: `text-sm font-semibold ${styles.title}`,\n                                    children: notification.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                notification.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: `text-sm mt-1 ${styles.message}`,\n                                    children: notification.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this),\n                                notification.actions && notification.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2 mt-3\",\n                                    children: notification.actions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: action.onClick,\n                                            className: `\n                      px-3 py-1.5 text-xs font-medium rounded-md border transition-colors\n                      ${action.variant === 'primary' ? `${styles.icon} border-[var(--color-border)] bg-[var(--color-background-tertiary)] hover:bg-[var(--color-card-hover)]` : 'text-[var(--color-foreground-muted)] border border-transparent hover:bg-[var(--color-background-tertiary)]'}\n                    `,\n                                            children: action.label\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        notification.dismissible !== false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onDismiss(notification.id),\n                            className: `\n                flex-shrink-0 p-1 rounded-md transition-colors\n                text-[var(--color-foreground-muted)] hover:bg-[var(--color-background-tertiary)]\n              `,\n                            \"aria-label\": \"关闭通知\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this),\n                notification.duration && notification.duration > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    className: `absolute bottom-0 left-0 h-1 rounded-bl-lg ${styles.icon} bg-current/20`,\n                    initial: {\n                        width: '100%'\n                    },\n                    animate: {\n                        width: '0%'\n                    },\n                    transition: {\n                        duration: notification.duration / 1000,\n                        ease: 'linear'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationItem.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/notification/NotificationItem.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/notification/NotificationManager.tsx":
/*!*************************************************************!*\
  !*** ./src/components/notification/NotificationManager.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationManager: () => (/* binding */ NotificationManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _NotificationContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NotificationContext */ \"(ssr)/./src/components/notification/NotificationContext.tsx\");\n/* harmony import */ var _NotificationContainer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NotificationContainer */ \"(ssr)/./src/components/notification/NotificationContainer.tsx\");\n/* __next_internal_client_entry_do_not_use__ NotificationManager auto */ \n\n\n\nfunction NotificationManager({ position = 'top-right', maxNotifications = 5 }) {\n    const { notifications, dismiss } = (0,_NotificationContext__WEBPACK_IMPORTED_MODULE_2__.useNotification)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationContainer__WEBPACK_IMPORTED_MODULE_3__.NotificationContainer, {\n        notifications: notifications,\n        onDismiss: dismiss,\n        position: position,\n        maxNotifications: maxNotifications\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\NotificationManager.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ub3RpZmljYXRpb24vTm90aWZpY2F0aW9uTWFuYWdlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFMEI7QUFDOEI7QUFDUTtBQU96RCxTQUFTRyxvQkFBb0IsRUFDbENDLFdBQVcsV0FBVyxFQUN0QkMsbUJBQW1CLENBQUMsRUFDSztJQUN6QixNQUFNLEVBQUVDLGFBQWEsRUFBRUMsT0FBTyxFQUFFLEdBQUdOLHFFQUFlQTtJQUVsRCxxQkFDRSw4REFBQ0MseUVBQXFCQTtRQUNwQkksZUFBZUE7UUFDZkUsV0FBV0Q7UUFDWEgsVUFBVUE7UUFDVkMsa0JBQWtCQTs7Ozs7O0FBR3hCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXG5vdGlmaWNhdGlvblxcTm90aWZpY2F0aW9uTWFuYWdlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgdXNlTm90aWZpY2F0aW9uIH0gZnJvbSAnLi9Ob3RpZmljYXRpb25Db250ZXh0JztcclxuaW1wb3J0IHsgTm90aWZpY2F0aW9uQ29udGFpbmVyIH0gZnJvbSAnLi9Ob3RpZmljYXRpb25Db250YWluZXInO1xyXG5cclxuaW50ZXJmYWNlIE5vdGlmaWNhdGlvbk1hbmFnZXJQcm9wcyB7XHJcbiAgcG9zaXRpb24/OiAndG9wLXJpZ2h0JyB8ICd0b3AtbGVmdCcgfCAnYm90dG9tLXJpZ2h0JyB8ICdib3R0b20tbGVmdCc7XHJcbiAgbWF4Tm90aWZpY2F0aW9ucz86IG51bWJlcjtcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIE5vdGlmaWNhdGlvbk1hbmFnZXIoeyBcclxuICBwb3NpdGlvbiA9ICd0b3AtcmlnaHQnLFxyXG4gIG1heE5vdGlmaWNhdGlvbnMgPSA1IFxyXG59OiBOb3RpZmljYXRpb25NYW5hZ2VyUHJvcHMpIHtcclxuICBjb25zdCB7IG5vdGlmaWNhdGlvbnMsIGRpc21pc3MgfSA9IHVzZU5vdGlmaWNhdGlvbigpO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPE5vdGlmaWNhdGlvbkNvbnRhaW5lclxyXG4gICAgICBub3RpZmljYXRpb25zPXtub3RpZmljYXRpb25zfVxyXG4gICAgICBvbkRpc21pc3M9e2Rpc21pc3N9XHJcbiAgICAgIHBvc2l0aW9uPXtwb3NpdGlvbn1cclxuICAgICAgbWF4Tm90aWZpY2F0aW9ucz17bWF4Tm90aWZpY2F0aW9uc31cclxuICAgIC8+XHJcbiAgKTtcclxufSAiXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VOb3RpZmljYXRpb24iLCJOb3RpZmljYXRpb25Db250YWluZXIiLCJOb3RpZmljYXRpb25NYW5hZ2VyIiwicG9zaXRpb24iLCJtYXhOb3RpZmljYXRpb25zIiwibm90aWZpY2F0aW9ucyIsImRpc21pc3MiLCJvbkRpc21pc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/notification/NotificationManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/notification/examples/NotificationDemo.tsx":
/*!*******************************************************************!*\
  !*** ./src/components/notification/examples/NotificationDemo.tsx ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationDemo: () => (/* binding */ NotificationDemo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _NotificationContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../NotificationContext */ \"(ssr)/./src/components/notification/NotificationContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,CheckCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ NotificationDemo auto */ \n\n\n\nfunction NotificationDemo() {\n    const notification = (0,_NotificationContext__WEBPACK_IMPORTED_MODULE_2__.useNotification)();\n    const showSuccess = ()=>{\n        notification.success('操作成功', '您的操作已成功完成');\n    };\n    const showError = ()=>{\n        notification.error('操作失败', '系统遇到了一个错误，请稍后重试');\n    };\n    const showWarning = ()=>{\n        notification.warning('注意', '这是一个重要的警告信息');\n    };\n    const showInfo = ()=>{\n        notification.info('提示', '这是一条信息提示');\n    };\n    const showCustomNotification = ()=>{\n        notification.show({\n            type: 'info',\n            title: '自定义通知',\n            message: '这是一个带有操作按钮的通知',\n            duration: 0,\n            actions: [\n                {\n                    label: '确认',\n                    variant: 'primary',\n                    onClick: ()=>{\n                        notification.success('已确认', '您已确认此操作');\n                    }\n                },\n                {\n                    label: '取消',\n                    variant: 'secondary',\n                    onClick: ()=>{\n                        notification.warning('已取消', '操作已取消');\n                    }\n                }\n            ]\n        });\n    };\n    const showPersistentNotification = ()=>{\n        notification.show({\n            type: 'warning',\n            title: '持久通知',\n            message: '这个通知不会自动消失，需要手动关闭',\n            duration: 0,\n            dismissible: true\n        });\n    };\n    const showWithCustomIcon = ()=>{\n        notification.show({\n            type: 'info',\n            title: '自定义图标',\n            message: '这个通知使用了自定义图标',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                lineNumber: 66,\n                columnNumber: 13\n            }, this)\n        });\n    };\n    const clearAllNotifications = ()=>{\n        notification.dismissAll();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-theme-foreground mb-2\",\n                        children: \"通知系统演示\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-theme-foreground-muted\",\n                        children: \"点击下方按钮体验不同类型的通知效果\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-theme-foreground flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"基础类型\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: showSuccess,\n                                className: \"w-full px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors\",\n                                children: \"成功通知\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: showError,\n                                className: \"w-full px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors\",\n                                children: \"错误通知\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: showWarning,\n                                className: \"w-full px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors\",\n                                children: \"警告通知\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: showInfo,\n                                className: \"w-full px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors\",\n                                children: \"信息通知\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-theme-foreground flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_CheckCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 text-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"高级功能\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: showCustomNotification,\n                                className: \"w-full px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors\",\n                                children: \"带操作按钮\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: showPersistentNotification,\n                                className: \"w-full px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors\",\n                                children: \"持久通知\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: showWithCustomIcon,\n                                className: \"w-full px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white rounded-lg transition-colors\",\n                                children: \"自定义图标\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearAllNotifications,\n                                className: \"w-full px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors\",\n                                children: \"清除所有通知\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 p-4 bg-theme-background-secondary rounded-lg border border-theme-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-theme-foreground mb-2\",\n                        children: [\n                            \"当前通知数量: \",\n                            notification.notifications.length\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    notification.notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            notification.notifications.slice(0, 3).map((notif)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-theme-foreground-muted\",\n                                    children: [\n                                        notif.type,\n                                        \": \",\n                                        notif.title\n                                    ]\n                                }, notif.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this)),\n                            notification.notifications.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-theme-foreground-muted\",\n                                children: [\n                                    \"还有 \",\n                                    notification.notifications.length - 3,\n                                    \" 条通知...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\notification\\\\examples\\\\NotificationDemo.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/notification/examples/NotificationDemo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ConversationContext.tsx":
/*!**********************************************!*\
  !*** ./src/contexts/ConversationContext.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationProvider: () => (/* binding */ ConversationProvider),\n/* harmony export */   useConversations: () => (/* binding */ useConversations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ConversationProvider,useConversations auto */ \n\nconst ConversationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// 全局缓存配置\nconst CACHE_DURATION = 30000; // 30秒缓存\nlet globalConversationsCache = [];\nlet globalCacheTimestamp = 0;\nlet isLoadingGlobal = false;\nfunction ConversationProvider({ children }) {\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 防止重复请求的引用\n    const loadingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 检查缓存是否有效\n    const isCacheValid = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[isCacheValid]\": ()=>{\n            const now = Date.now();\n            return globalConversationsCache.length > 0 && now - globalCacheTimestamp < CACHE_DURATION;\n        }\n    }[\"ConversationProvider.useCallback[isCacheValid]\"], []);\n    // 加载对话列表\n    const loadConversations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[loadConversations]\": async (forceRefresh = false)=>{\n            // 如果正在加载中，直接返回\n            if (loadingRef.current || isLoadingGlobal) {\n                return;\n            }\n            // 如果不强制刷新且缓存有效，使用缓存\n            if (!forceRefresh && isCacheValid()) {\n                setConversations(globalConversationsCache);\n                return;\n            }\n            try {\n                loadingRef.current = true;\n                isLoadingGlobal = true;\n                setLoading(true);\n                setError(null);\n                // 取消之前的请求\n                if (abortControllerRef.current) {\n                    abortControllerRef.current.abort();\n                }\n                // 创建新的AbortController\n                abortControllerRef.current = new AbortController();\n                console.log('[ConversationProvider] 开始加载对话列表...');\n                const response = await fetch('/api/conversations', {\n                    signal: abortControllerRef.current.signal\n                });\n                const data = await response.json();\n                if (data.success) {\n                    const conversationList = data.conversations || [];\n                    // 更新全局缓存\n                    globalConversationsCache = conversationList;\n                    globalCacheTimestamp = Date.now();\n                    setConversations(conversationList);\n                    console.log(`[ConversationProvider] 成功加载 ${conversationList.length} 个对话`);\n                } else {\n                    setError(data.error || '加载对话列表失败');\n                }\n            } catch (err) {\n                if (err instanceof Error && err.name === 'AbortError') {\n                    console.log('[ConversationProvider] 请求被取消');\n                    return;\n                }\n                setError('网络错误，加载对话列表失败');\n                console.error('加载对话列表失败:', err);\n            } finally{\n                loadingRef.current = false;\n                isLoadingGlobal = false;\n                setLoading(false);\n                abortControllerRef.current = null;\n            }\n        }\n    }[\"ConversationProvider.useCallback[loadConversations]\"], [\n        isCacheValid\n    ]);\n    // 创建新对话\n    const createConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[createConversation]\": async (title, model)=>{\n            try {\n                setError(null);\n                console.log(`[ConversationProvider] 开始创建对话: ${title}, 模型: ${model}`);\n                const response = await fetch('/api/conversations', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        title,\n                        model\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(`[ConversationProvider] 对话创建成功:`, data.conversation);\n                    // 刷新对话列表\n                    await loadConversations(true);\n                    // 切换到新创建的对话\n                    setCurrentConversation(data.conversation);\n                    return data.conversation.id;\n                } else {\n                    console.error(`[ConversationProvider] 创建对话失败:`, data.error);\n                    setError(data.error || '创建对话失败');\n                    return null;\n                }\n            } catch (err) {\n                console.error(`[ConversationProvider] 创建对话异常:`, err);\n                setError('网络错误，创建对话失败');\n                return null;\n            }\n        }\n    }[\"ConversationProvider.useCallback[createConversation]\"], [\n        loadConversations\n    ]);\n    // 切换对话\n    const switchConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[switchConversation]\": async (id)=>{\n            try {\n                setError(null);\n                console.log(`[ConversationProvider] 开始切换到对话 ${id}`);\n                const response = await fetch(`/api/conversations/${id}`);\n                const data = await response.json();\n                if (data.success) {\n                    console.log(`[ConversationProvider] 成功获取对话 ${id} 信息:`, data.conversation);\n                    setCurrentConversation(data.conversation);\n                } else {\n                    console.error(`[ConversationProvider] 切换对话 ${id} 失败:`, data.error);\n                    setError(data.error || '切换对话失败');\n                    throw new Error(data.error || '切换对话失败');\n                }\n            } catch (err) {\n                console.error(`[ConversationProvider] 切换对话 ${id} 异常:`, err);\n                setError('网络错误，切换对话失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[switchConversation]\"], []);\n    // 删除对话\n    const deleteConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[deleteConversation]\": async (id)=>{\n            try {\n                setError(null);\n                console.log(`[ConversationProvider] 开始删除对话 ${id}`);\n                const response = await fetch(`/api/conversations/${id}`, {\n                    method: 'DELETE'\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(`[ConversationProvider] 对话 ${id} 删除成功`);\n                    // 从本地状态中移除\n                    setConversations({\n                        \"ConversationProvider.useCallback[deleteConversation]\": (prev)=>prev.filter({\n                                \"ConversationProvider.useCallback[deleteConversation]\": (conv)=>conv.id !== id\n                            }[\"ConversationProvider.useCallback[deleteConversation]\"])\n                    }[\"ConversationProvider.useCallback[deleteConversation]\"]);\n                    // 更新全局缓存\n                    globalConversationsCache = globalConversationsCache.filter({\n                        \"ConversationProvider.useCallback[deleteConversation]\": (conv)=>conv.id !== id\n                    }[\"ConversationProvider.useCallback[deleteConversation]\"]);\n                    // 如果删除的是当前对话，清空当前对话\n                    if (currentConversation?.id === id) {\n                        setCurrentConversation(null);\n                    }\n                } else {\n                    console.error(`[ConversationProvider] 删除对话 ${id} 失败:`, data.error);\n                    setError(data.error || '删除对话失败');\n                    throw new Error(data.error || '删除对话失败');\n                }\n            } catch (err) {\n                console.error(`[ConversationProvider] 删除对话 ${id} 异常:`, err);\n                setError('网络错误，删除对话失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[deleteConversation]\"], [\n        currentConversation?.id\n    ]);\n    // 更新对话标题\n    const updateConversationTitle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[updateConversationTitle]\": async (id, title)=>{\n            try {\n                setError(null);\n                console.log(`[ConversationProvider] 开始更新对话 ${id} 标题: ${title}`);\n                const response = await fetch(`/api/conversations/${id}`, {\n                    method: 'PATCH',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        title\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(`[ConversationProvider] 对话 ${id} 标题更新成功`);\n                    // 更新本地状态\n                    setConversations({\n                        \"ConversationProvider.useCallback[updateConversationTitle]\": (prev)=>prev.map({\n                                \"ConversationProvider.useCallback[updateConversationTitle]\": (conv)=>conv.id === id ? {\n                                        ...conv,\n                                        title\n                                    } : conv\n                            }[\"ConversationProvider.useCallback[updateConversationTitle]\"])\n                    }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    // 更新全局缓存\n                    globalConversationsCache = globalConversationsCache.map({\n                        \"ConversationProvider.useCallback[updateConversationTitle]\": (conv)=>conv.id === id ? {\n                                ...conv,\n                                title\n                            } : conv\n                    }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    // 如果是当前对话，也更新当前对话状态\n                    if (currentConversation?.id === id) {\n                        setCurrentConversation({\n                            \"ConversationProvider.useCallback[updateConversationTitle]\": (prev)=>prev ? {\n                                    ...prev,\n                                    title\n                                } : null\n                        }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    }\n                } else {\n                    console.error(`[ConversationProvider] 更新对话 ${id} 标题失败:`, data.error);\n                    setError(data.error || '更新对话标题失败');\n                    throw new Error(data.error || '更新对话标题失败');\n                }\n            } catch (err) {\n                console.error(`[ConversationProvider] 更新对话 ${id} 标题异常:`, err);\n                setError('网络错误，更新对话标题失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[updateConversationTitle]\"], [\n        currentConversation?.id\n    ]);\n    // 初始化时加载对话列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationProvider.useEffect\": ()=>{\n            loadConversations();\n        }\n    }[\"ConversationProvider.useEffect\"], [\n        loadConversations\n    ]);\n    // 组件卸载时清理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationProvider.useEffect\": ()=>{\n            return ({\n                \"ConversationProvider.useEffect\": ()=>{\n                    if (abortControllerRef.current) {\n                        abortControllerRef.current.abort();\n                    }\n                }\n            })[\"ConversationProvider.useEffect\"];\n        }\n    }[\"ConversationProvider.useEffect\"], []);\n    const value = {\n        conversations,\n        currentConversation,\n        loading,\n        error,\n        loadConversations,\n        createConversation,\n        switchConversation,\n        deleteConversation,\n        updateConversationTitle,\n        setCurrentConversation\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConversationContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\contexts\\\\ConversationContext.tsx\",\n        lineNumber: 286,\n        columnNumber: 5\n    }, this);\n}\nfunction useConversations() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ConversationContext);\n    if (context === undefined) {\n        throw new Error('useConversations must be used within a ConversationProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ConversationContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/theme.ts":
/*!**************************!*\
  !*** ./src/lib/theme.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyTheme: () => (/* binding */ applyTheme),\n/* harmony export */   getSavedTheme: () => (/* binding */ getSavedTheme),\n/* harmony export */   getSystemTheme: () => (/* binding */ getSystemTheme),\n/* harmony export */   initializeTheme: () => (/* binding */ initializeTheme),\n/* harmony export */   saveTheme: () => (/* binding */ saveTheme)\n/* harmony export */ });\n// 主题类型定义\n// 获取系统主题偏好\nconst getSystemTheme = ()=>{\n    if (false) {}\n    return 'light';\n};\n// 从localStorage获取保存的主题\nconst getSavedTheme = ()=>{\n    if (false) {}\n    return null;\n};\n// 保存主题到localStorage\nconst saveTheme = (theme)=>{\n    if (false) {}\n};\n// 应用主题到DOM（带过渡效果）\nconst applyTheme = (theme)=>{\n    if (false) {}\n};\n// 初始化主题\nconst initializeTheme = ()=>{\n    const savedTheme = getSavedTheme();\n    const theme = savedTheme || getSystemTheme();\n    // 首次加载时直接应用主题，不带过渡\n    if (false) {}\n    return theme;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/theme.ts\n");

/***/ }),

/***/ "(ssr)/./src/theme/components/ColorThemeScript.tsx":
/*!***************************************************!*\
  !*** ./src/theme/components/ColorThemeScript.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorThemeScript: () => (/* binding */ ColorThemeScript)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ColorThemeScript auto */ \n\nconst THEME_STORAGE_KEY = 'color-theme';\n// This script is injected into the <head> to prevent FOUC (Flash of Unstyled Content)\n// for the color theme. It runs before the page content renders.\nconst colorThemeScript = `\n(function() {\n  try {\n    const theme = localStorage.getItem('${THEME_STORAGE_KEY}');\n    if (theme) {\n      document.documentElement.setAttribute('data-color-theme', theme);\n    } else {\n      // You can set a default theme if none is found\n      document.documentElement.setAttribute('data-color-theme', 'kun');\n    }\n  } catch (e) {\n    console.warn('Could not set color theme from localStorage', e);\n  }\n})();\n`;\nfunction ColorThemeScript() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        dangerouslySetInnerHTML: {\n            __html: colorThemeScript\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ColorThemeScript.tsx\",\n        lineNumber: 26,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdGhlbWUvY29tcG9uZW50cy9Db2xvclRoZW1lU2NyaXB0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFMEI7QUFFMUIsTUFBTUMsb0JBQW9CO0FBRTFCLHNGQUFzRjtBQUN0RixnRUFBZ0U7QUFDaEUsTUFBTUMsbUJBQW1CLENBQUM7Ozt3Q0FHYyxFQUFFRCxrQkFBa0I7Ozs7Ozs7Ozs7O0FBVzVELENBQUM7QUFFTSxTQUFTRTtJQUNkLHFCQUFPLDhEQUFDQztRQUFPQyx5QkFBeUI7WUFBRUMsUUFBUUo7UUFBaUI7Ozs7OztBQUNyRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcc3JjXFx0aGVtZVxcY29tcG9uZW50c1xcQ29sb3JUaGVtZVNjcmlwdC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuXHJcbmNvbnN0IFRIRU1FX1NUT1JBR0VfS0VZID0gJ2NvbG9yLXRoZW1lJztcclxuXHJcbi8vIFRoaXMgc2NyaXB0IGlzIGluamVjdGVkIGludG8gdGhlIDxoZWFkPiB0byBwcmV2ZW50IEZPVUMgKEZsYXNoIG9mIFVuc3R5bGVkIENvbnRlbnQpXHJcbi8vIGZvciB0aGUgY29sb3IgdGhlbWUuIEl0IHJ1bnMgYmVmb3JlIHRoZSBwYWdlIGNvbnRlbnQgcmVuZGVycy5cclxuY29uc3QgY29sb3JUaGVtZVNjcmlwdCA9IGBcclxuKGZ1bmN0aW9uKCkge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCB0aGVtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCcke1RIRU1FX1NUT1JBR0VfS0VZfScpO1xyXG4gICAgaWYgKHRoZW1lKSB7XHJcbiAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zZXRBdHRyaWJ1dGUoJ2RhdGEtY29sb3ItdGhlbWUnLCB0aGVtZSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBZb3UgY2FuIHNldCBhIGRlZmF1bHQgdGhlbWUgaWYgbm9uZSBpcyBmb3VuZFxyXG4gICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc2V0QXR0cmlidXRlKCdkYXRhLWNvbG9yLXRoZW1lJywgJ2t1bicpO1xyXG4gICAgfVxyXG4gIH0gY2F0Y2ggKGUpIHtcclxuICAgIGNvbnNvbGUud2FybignQ291bGQgbm90IHNldCBjb2xvciB0aGVtZSBmcm9tIGxvY2FsU3RvcmFnZScsIGUpO1xyXG4gIH1cclxufSkoKTtcclxuYDtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBDb2xvclRoZW1lU2NyaXB0KCkge1xyXG4gIHJldHVybiA8c2NyaXB0IGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MPXt7IF9faHRtbDogY29sb3JUaGVtZVNjcmlwdCB9fSAvPjtcclxufSAiXSwibmFtZXMiOlsiUmVhY3QiLCJUSEVNRV9TVE9SQUdFX0tFWSIsImNvbG9yVGhlbWVTY3JpcHQiLCJDb2xvclRoZW1lU2NyaXB0Iiwic2NyaXB0IiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJfX2h0bWwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/theme/components/ColorThemeScript.tsx\n");

/***/ }),

/***/ "(ssr)/./src/theme/components/ColorThemeSwitcher.tsx":
/*!*****************************************************!*\
  !*** ./src/theme/components/ColorThemeSwitcher.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorThemeSwitcher: () => (/* binding */ ColorThemeSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ColorThemeSwitcher auto */ \n\nconst THEME_STORAGE_KEY = 'color-theme';\nconst themes = [\n    {\n        name: 'kun',\n        color: '#6a5ac2',\n        displayName: '默认'\n    },\n    {\n        name: 'green',\n        color: '#4D564F',\n        displayName: '芦苇绿'\n    },\n    {\n        name: 'purple',\n        color: '#6a5ac2',\n        displayName: '优雅紫'\n    },\n    {\n        name: 'orange',\n        color: '#E07800',\n        displayName: '活力橙'\n    },\n    {\n        name: 'blue',\n        color: '#284B7B',\n        displayName: '伯克利蓝'\n    },\n    {\n        name: 'raspberry',\n        color: '#EC4680',\n        displayName: '覆盆子'\n    },\n    {\n        name: 'moonstone',\n        color: '#61A0AF',\n        displayName: '月长石'\n    }\n];\nfunction ColorThemeSwitcher() {\n    const [activeTheme, setActiveTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('kun');\n    const [isPaletteOpen, setPaletteOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const paletteRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorThemeSwitcher.useEffect\": ()=>{\n            const savedTheme = localStorage.getItem(THEME_STORAGE_KEY) || 'kun';\n            setActiveTheme(savedTheme);\n            document.documentElement.setAttribute('data-color-theme', savedTheme);\n        }\n    }[\"ColorThemeSwitcher.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ColorThemeSwitcher.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"ColorThemeSwitcher.useEffect.handleClickOutside\": (event)=>{\n                    if (paletteRef.current && !paletteRef.current.contains(event.target)) {\n                        setPaletteOpen(false);\n                    }\n                }\n            }[\"ColorThemeSwitcher.useEffect.handleClickOutside\"];\n            if (isPaletteOpen) {\n                document.addEventListener('mousedown', handleClickOutside);\n            }\n            return ({\n                \"ColorThemeSwitcher.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"ColorThemeSwitcher.useEffect\"];\n        }\n    }[\"ColorThemeSwitcher.useEffect\"], [\n        isPaletteOpen\n    ]);\n    const changeTheme = (themeName)=>{\n        document.documentElement.setAttribute('data-color-theme', themeName);\n        localStorage.setItem(THEME_STORAGE_KEY, themeName);\n        setActiveTheme(themeName);\n        setPaletteOpen(false);\n    };\n    const activeColor = themes.find((t)=>t.name === activeTheme)?.color || '#F36D68';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: paletteRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setPaletteOpen(!isPaletteOpen),\n                className: \"sidebar-expanded-only w-5 h-5 rounded-full bg-gradient-to-br from-theme-primary to-theme-primary-hover flex-shrink-0 transition-all duration-300 transform hover:scale-110\",\n                style: {\n                    backgroundColor: activeColor,\n                    backgroundImage: `linear-gradient(135deg, ${activeColor} 0%, var(--color-primary-hover) 100%)`\n                },\n                \"aria-label\": \"选择主题颜色\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ColorThemeSwitcher.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            isPaletteOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-full mb-3 left-1/2 -translate-x-1/2 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center space-y-3 p-3 bg-theme-card/50 backdrop-blur-sm rounded-lg\",\n                    children: themes.map((theme)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            title: theme.displayName,\n                            onClick: ()=>changeTheme(theme.name),\n                            className: \"relative group w-6 h-6 rounded-full transition-all duration-200 transform hover:scale-125 focus:outline-none\",\n                            style: {\n                                backgroundColor: theme.color,\n                                boxShadow: activeTheme === theme.name ? `0 0 0 2px var(--color-background), 0 0 0 4px ${theme.color}` : '0 0 0 1px rgba(0,0,0,0.1)'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute top-1/2 -translate-y-1/2 left-full ml-3 px-2 py-1 rounded-md text-xs bg-gray-900/80 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none\",\n                                children: theme.displayName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ColorThemeSwitcher.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 17\n                            }, this)\n                        }, theme.name, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ColorThemeSwitcher.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ColorThemeSwitcher.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ColorThemeSwitcher.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ColorThemeSwitcher.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/theme/components/ColorThemeSwitcher.tsx\n");

/***/ }),

/***/ "(ssr)/./src/theme/components/ThemeToggle.tsx":
/*!**********************************************!*\
  !*** ./src/theme/components/ThemeToggle.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeSwitch: () => (/* binding */ ThemeSwitch),\n/* harmony export */   ThemeToggle: () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _theme_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/theme/contexts/ThemeContext */ \"(ssr)/./src/theme/contexts/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ ThemeToggle,ThemeSwitch auto */ \n\n\n\nfunction ThemeToggle({ className = '', size = 'md', variant = 'button', showLabel = false }) {\n    const { theme, toggleTheme, isDark } = (0,_theme_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.useThemeToggle)();\n    // 尺寸配置\n    const sizeConfig = {\n        sm: {\n            button: 'w-8 h-8',\n            icon: 'w-4 h-4',\n            text: 'text-xs'\n        },\n        md: {\n            button: 'w-10 h-10',\n            icon: 'w-5 h-5',\n            text: 'text-sm'\n        },\n        lg: {\n            button: 'w-12 h-12',\n            icon: 'w-6 h-6',\n            text: 'text-base'\n        }\n    };\n    const config = sizeConfig[size];\n    if (variant === 'icon') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: toggleTheme,\n            className: `\n          ${config.button}\n          flex items-center justify-center\n          rounded-full\n          bg-theme-background-tertiary hover:bg-theme-card-hover\n          text-theme-foreground-muted hover:text-theme-foreground\n          transition-all duration-200 ease-in-out\n          transform hover:scale-105 active:scale-95\n          focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-offset-2\n          focus:ring-offset-theme-background\n          ${className}\n        `,\n            title: isDark ? '切换到浅色模式' : '切换到深色模式',\n            \"aria-label\": isDark ? '切换到浅色模式' : '切换到深色模式',\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `relative ${config.icon}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: `\n              ${config.icon}\n              absolute inset-0\n              transition-all duration-300 ease-in-out\n              ${isDark ? 'opacity-0 rotate-90 scale-0' : 'opacity-100 rotate-0 scale-100'}\n            `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: `\n              ${config.icon}\n              absolute inset-0\n              transition-all duration-300 ease-in-out\n              ${isDark ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 -rotate-90 scale-0'}\n            `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: `\n        flex items-center gap-2 px-3 py-2\n        rounded-lg\n        bg-theme-background-tertiary hover:bg-theme-card-hover\n        text-theme-foreground-secondary hover:text-theme-foreground\n        transition-all duration-200 ease-in-out\n        transform hover:scale-105 active:scale-95\n        focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-offset-2\n        focus:ring-offset-theme-background\n        ${config.text}\n        ${className}\n      `,\n        title: isDark ? '切换到浅色模式' : '切换到深色模式',\n        \"aria-label\": isDark ? '切换到浅色模式' : '切换到深色模式',\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: `\n            ${config.icon}\n            absolute inset-0\n            transition-all duration-300 ease-in-out\n            ${isDark ? 'opacity-0 rotate-90 scale-0' : 'opacity-100 rotate-0 scale-100'}\n          `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: `\n            ${config.icon}\n            absolute inset-0\n            transition-all duration-300 ease-in-out\n            ${isDark ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 -rotate-90 scale-0'}\n          `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-medium\",\n                children: isDark ? '浅色' : '深色'\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n// 简化版主题切换开关\nfunction ThemeSwitch({ className = '' }) {\n    const { isDark, toggleTheme } = (0,_theme_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.useThemeToggle)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: `\n        relative inline-flex h-6 w-11 items-center rounded-full\n        transition-colors duration-200 ease-in-out\n        focus:outline-none focus:ring-2 focus:ring-theme-primary focus:ring-offset-2\n        focus:ring-offset-theme-background\n        ${isDark ? 'bg-theme-primary' : 'bg-theme-border-secondary'}\n        ${className}\n      `,\n        role: \"switch\",\n        \"aria-checked\": isDark,\n        \"aria-label\": isDark ? '切换到浅色模式' : '切换到深色模式',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: `\n          inline-block h-4 w-4 transform rounded-full bg-white\n          transition-transform duration-200 ease-in-out\n          ${isDark ? 'translate-x-6' : 'translate-x-1'}\n        `\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/theme/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/theme/contexts/ThemeContext.tsx":
/*!*********************************************!*\
  !*** ./src/theme/contexts/ThemeContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useThemeToggle: () => (/* binding */ useThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_theme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/theme */ \"(ssr)/./src/lib/theme.ts\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme,useThemeToggle auto */ \n\n\n// 创建主题上下文\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// 主题提供者组件\nfunction ThemeProvider({ children, defaultTheme = 'light' }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 设置主题的函数\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.applyTheme)(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.saveTheme)(newTheme);\n    };\n    // 切换主题的函数\n    const toggleTheme = ()=>{\n        const newTheme = theme === 'light' ? 'dark' : 'light';\n        setTheme(newTheme);\n    };\n    // 初始化主题\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const initialTheme = (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.initializeTheme)();\n            setThemeState(initialTheme);\n            setIsInitialized(true);\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // 监听系统主题变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!isInitialized) return;\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": (e)=>{\n                    // 只有在没有手动设置主题时才跟随系统主题\n                    const savedTheme = localStorage.getItem('theme');\n                    if (!savedTheme) {\n                        const systemTheme = e.matches ? 'dark' : 'light';\n                        setTheme(systemTheme);\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>{\n                    mediaQuery.removeEventListener('change', handleChange);\n                }\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        isInitialized\n    ]);\n    // 提供主题配置\n    const themeConfig = {\n        theme,\n        setTheme,\n        toggleTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: themeConfig,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\theme\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n// 使用主题的Hook\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n// 主题切换Hook（简化版本）\nfunction useThemeToggle() {\n    const { theme, toggleTheme } = useTheme();\n    return {\n        theme,\n        toggleTheme,\n        isDark: theme === 'dark',\n        isLight: theme === 'light'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/theme/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();