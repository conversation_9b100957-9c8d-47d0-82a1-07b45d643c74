/**
 * API调用追踪器 - 用于监控和分析API调用情况
 */

interface ApiCallRecord {
  url: string;
  method: string;
  timestamp: number;
  duration?: number;
  status?: number;
  source: string; // 调用来源
}

class ApiCallTracker {
  private calls: ApiCallRecord[] = [];
  private isTracking = false;
  private originalFetch: typeof fetch | null = null;

  constructor() {
    // 只在客户端环境下初始化
    if (typeof window !== 'undefined') {
      this.originalFetch = window.fetch;
    }
  }

  /**
   * 开始追踪API调用
   */
  startTracking() {
    // 检查是否在客户端环境
    if (typeof window === 'undefined' || !this.originalFetch) {
      console.warn('[ApiCallTracker] 只能在客户端环境下使用');
      return;
    }

    if (this.isTracking) return;

    this.isTracking = true;
    this.calls = [];

    console.log('[ApiCallTracker] 开始追踪API调用...');

    // 拦截fetch请求
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input.toString();
      const method = init?.method || 'GET';
      const startTime = Date.now();
      
      // 获取调用栈信息
      const stack = new Error().stack || '';
      const source = this.extractSource(stack);
      
      const record: ApiCallRecord = {
        url,
        method,
        timestamp: startTime,
        source,
      };
      
      try {
        const response = await this.originalFetch!(input, init);
        
        record.duration = Date.now() - startTime;
        record.status = response.status;
        
        this.calls.push(record);
        
        console.log(`[ApiCallTracker] ${method} ${url} - ${response.status} (${record.duration}ms) - ${source}`);
        
        return response;
      } catch (error) {
        record.duration = Date.now() - startTime;
        this.calls.push(record);
        
        console.error(`[ApiCallTracker] ${method} ${url} - ERROR (${record.duration}ms) - ${source}`, error);
        throw error;
      }
    };
  }

  /**
   * 停止追踪API调用
   */
  stopTracking() {
    if (typeof window === 'undefined' || !this.originalFetch || !this.isTracking) return;

    this.isTracking = false;
    window.fetch = this.originalFetch;

    console.log('[ApiCallTracker] 停止追踪API调用');
  }

  /**
   * 获取追踪结果
   */
  getResults() {
    return {
      totalCalls: this.calls.length,
      calls: [...this.calls],
      duplicates: this.findDuplicates(),
      summary: this.generateSummary(),
    };
  }

  /**
   * 清除追踪记录
   */
  clear() {
    this.calls = [];
  }

  /**
   * 查找重复调用
   */
  private findDuplicates() {
    const duplicates: { [key: string]: ApiCallRecord[] } = {};
    const seen = new Map<string, ApiCallRecord[]>();
    
    this.calls.forEach(call => {
      const key = `${call.method}:${call.url}`;
      if (!seen.has(key)) {
        seen.set(key, []);
      }
      seen.get(key)!.push(call);
    });
    
    seen.forEach((calls, key) => {
      if (calls.length > 1) {
        duplicates[key] = calls;
      }
    });
    
    return duplicates;
  }

  /**
   * 生成统计摘要
   */
  private generateSummary() {
    const urlCounts = new Map<string, number>();
    const methodCounts = new Map<string, number>();
    const sourceCounts = new Map<string, number>();
    
    this.calls.forEach(call => {
      // URL统计
      const urlCount = urlCounts.get(call.url) || 0;
      urlCounts.set(call.url, urlCount + 1);
      
      // 方法统计
      const methodCount = methodCounts.get(call.method) || 0;
      methodCounts.set(call.method, methodCount + 1);
      
      // 来源统计
      const sourceCount = sourceCounts.get(call.source) || 0;
      sourceCounts.set(call.source, sourceCount + 1);
    });
    
    return {
      byUrl: Object.fromEntries(urlCounts),
      byMethod: Object.fromEntries(methodCounts),
      bySource: Object.fromEntries(sourceCounts),
      totalDuration: this.calls.reduce((sum, call) => sum + (call.duration || 0), 0),
      averageDuration: this.calls.length > 0 
        ? this.calls.reduce((sum, call) => sum + (call.duration || 0), 0) / this.calls.length 
        : 0,
    };
  }

  /**
   * 从调用栈中提取来源信息
   */
  private extractSource(stack: string): string {
    const lines = stack.split('\n');
    
    // 查找第一个非fetch相关的调用
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i];
      if (line.includes('at ') && !line.includes('fetch') && !line.includes('ApiCallTracker')) {
        // 提取文件名和行号
        const match = line.match(/at\s+(.+?)\s+\((.+?):(\d+):(\d+)\)/);
        if (match) {
          const [, functionName, filePath, lineNumber] = match;
          const fileName = filePath.split('/').pop() || filePath;
          return `${functionName} (${fileName}:${lineNumber})`;
        }
        
        // 简化格式
        const simpleMatch = line.match(/at\s+(.+)/);
        if (simpleMatch) {
          return simpleMatch[1].trim();
        }
      }
    }
    
    return 'unknown';
  }

  /**
   * 打印详细报告
   */
  printReport() {
    const results = this.getResults();
    
    console.group('📊 API调用追踪报告');
    console.log(`总调用次数: ${results.totalCalls}`);
    console.log(`总耗时: ${results.summary.totalDuration}ms`);
    console.log(`平均耗时: ${results.summary.averageDuration.toFixed(2)}ms`);
    
    console.group('🔄 重复调用');
    Object.entries(results.duplicates).forEach(([key, calls]) => {
      console.log(`${key}: ${calls.length}次调用`);
      calls.forEach((call, index) => {
        console.log(`  ${index + 1}. ${new Date(call.timestamp).toLocaleTimeString()} - ${call.duration}ms - ${call.source}`);
      });
    });
    console.groupEnd();
    
    console.group('📈 URL统计');
    Object.entries(results.summary.byUrl).forEach(([url, count]) => {
      console.log(`${url}: ${count}次`);
    });
    console.groupEnd();
    
    console.group('🏷️ 来源统计');
    Object.entries(results.summary.bySource).forEach(([source, count]) => {
      console.log(`${source}: ${count}次`);
    });
    console.groupEnd();
    
    console.groupEnd();
  }
}

// 创建全局实例
export const apiCallTracker = new ApiCallTracker();

// 客户端初始化函数
export function initApiTracker() {
  if (typeof window === 'undefined') return;

  // 开发环境下自动启用追踪
  if (process.env.NODE_ENV === 'development') {
    // 延迟启动，避免影响初始化
    setTimeout(() => {
      apiCallTracker.startTracking();
    }, 1000);
  }

  // 添加全局方法方便调试
  (window as any).apiTracker = {
    start: () => apiCallTracker.startTracking(),
    stop: () => apiCallTracker.stopTracking(),
    report: () => apiCallTracker.printReport(),
    clear: () => apiCallTracker.clear(),
    results: () => apiCallTracker.getResults(),
  };
}
