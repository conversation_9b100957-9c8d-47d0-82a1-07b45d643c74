"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/ConversationContext.tsx":
/*!**********************************************!*\
  !*** ./src/contexts/ConversationContext.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationProvider: () => (/* binding */ ConversationProvider),\n/* harmony export */   useConversations: () => (/* binding */ useConversations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ConversationProvider,useConversations auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ConversationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// 简化的全局缓存配置\nconst CACHE_DURATION = 30000; // 30秒缓存\nlet globalConversationsCache = [];\nlet globalCacheTimestamp = 0;\nlet isLoadingGlobal = false;\nfunction ConversationProvider(param) {\n    let { children } = param;\n    _s();\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 防止重复请求的引用\n    const loadingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 检查缓存是否有效\n    const isCacheValid = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[isCacheValid]\": ()=>{\n            const now = Date.now();\n            return globalConversationsCache.length > 0 && now - globalCacheTimestamp < CACHE_DURATION;\n        }\n    }[\"ConversationProvider.useCallback[isCacheValid]\"], []);\n    // 加载对话列表\n    const loadConversations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[loadConversations]\": async function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n            // 如果正在加载中，直接返回\n            if (loadingRef.current || isLoadingGlobal) {\n                return;\n            }\n            // 如果不强制刷新且缓存有效，使用缓存\n            if (!forceRefresh && isCacheValid()) {\n                setConversations(globalConversationsCache);\n                return;\n            }\n            try {\n                loadingRef.current = true;\n                isLoadingGlobal = true;\n                setLoading(true);\n                setError(null);\n                // 取消之前的请求\n                if (abortControllerRef.current) {\n                    abortControllerRef.current.abort();\n                }\n                // 创建新的AbortController\n                abortControllerRef.current = new AbortController();\n                console.log('[ConversationProvider] 开始加载对话列表...');\n                // 暂时使用原生fetch避免潜在的循环问题\n                const response = await fetch('/api/conversations', {\n                    signal: abortControllerRef.current.signal\n                });\n                const data = await response.json();\n                if (data.success) {\n                    const conversationList = data.conversations || [];\n                    // 更新全局缓存\n                    globalConversationsCache = conversationList;\n                    globalCacheTimestamp = Date.now();\n                    setConversations(conversationList);\n                    console.log(\"[ConversationProvider] 成功加载 \".concat(conversationList.length, \" 个对话\"));\n                } else {\n                    setError(data.error || '加载对话列表失败');\n                }\n            } catch (err) {\n                if (err instanceof Error && err.name === 'AbortError') {\n                    console.log('[ConversationProvider] 请求被取消');\n                    return;\n                }\n                setError('网络错误，加载对话列表失败');\n                console.error('加载对话列表失败:', err);\n            } finally{\n                loadingRef.current = false;\n                isLoadingGlobal = false;\n                setLoading(false);\n                abortControllerRef.current = null;\n            }\n        }\n    }[\"ConversationProvider.useCallback[loadConversations]\"], [\n        isCacheValid\n    ]);\n    // 创建新对话\n    const createConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[createConversation]\": async (title, model)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始创建对话: \".concat(title, \", 模型: \").concat(model));\n                const response = await fetch('/api/conversations', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        title,\n                        model\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 对话创建成功:\", data.conversation);\n                    // 刷新对话列表\n                    await loadConversations(true);\n                    // 切换到新创建的对话\n                    setCurrentConversation(data.conversation);\n                    return data.conversation.id;\n                } else {\n                    console.error(\"[ConversationProvider] 创建对话失败:\", data.error);\n                    setError(data.error || '创建对话失败');\n                    return null;\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 创建对话异常:\", err);\n                setError('网络错误，创建对话失败');\n                return null;\n            }\n        }\n    }[\"ConversationProvider.useCallback[createConversation]\"], [\n        loadConversations\n    ]);\n    // 切换对话\n    const switchConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[switchConversation]\": async (id)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始切换到对话 \".concat(id));\n                const response = await fetch(\"/api/conversations/\".concat(id));\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 成功获取对话 \".concat(id, \" 信息:\"), data.conversation);\n                    setCurrentConversation(data.conversation);\n                } else {\n                    console.error(\"[ConversationProvider] 切换对话 \".concat(id, \" 失败:\"), data.error);\n                    setError(data.error || '切换对话失败');\n                    throw new Error(data.error || '切换对话失败');\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 切换对话 \".concat(id, \" 异常:\"), err);\n                setError('网络错误，切换对话失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[switchConversation]\"], []);\n    // 删除对话\n    const deleteConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[deleteConversation]\": async (id)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始删除对话 \".concat(id));\n                const response = await fetch(\"/api/conversations/\".concat(id), {\n                    method: 'DELETE'\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 对话 \".concat(id, \" 删除成功\"));\n                    // 从本地状态中移除\n                    setConversations({\n                        \"ConversationProvider.useCallback[deleteConversation]\": (prev)=>prev.filter({\n                                \"ConversationProvider.useCallback[deleteConversation]\": (conv)=>conv.id !== id\n                            }[\"ConversationProvider.useCallback[deleteConversation]\"])\n                    }[\"ConversationProvider.useCallback[deleteConversation]\"]);\n                    // 更新全局缓存\n                    globalConversationsCache = globalConversationsCache.filter({\n                        \"ConversationProvider.useCallback[deleteConversation]\": (conv)=>conv.id !== id\n                    }[\"ConversationProvider.useCallback[deleteConversation]\"]);\n                    // 如果删除的是当前对话，清空当前对话\n                    if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === id) {\n                        setCurrentConversation(null);\n                    }\n                } else {\n                    console.error(\"[ConversationProvider] 删除对话 \".concat(id, \" 失败:\"), data.error);\n                    setError(data.error || '删除对话失败');\n                    throw new Error(data.error || '删除对话失败');\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 删除对话 \".concat(id, \" 异常:\"), err);\n                setError('网络错误，删除对话失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[deleteConversation]\"], [\n        currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id\n    ]);\n    // 更新对话标题\n    const updateConversationTitle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[updateConversationTitle]\": async (id, title)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始更新对话 \".concat(id, \" 标题: \").concat(title));\n                const response = await fetch(\"/api/conversations/\".concat(id), {\n                    method: 'PATCH',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        title\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 对话 \".concat(id, \" 标题更新成功\"));\n                    // 更新本地状态\n                    setConversations({\n                        \"ConversationProvider.useCallback[updateConversationTitle]\": (prev)=>prev.map({\n                                \"ConversationProvider.useCallback[updateConversationTitle]\": (conv)=>conv.id === id ? {\n                                        ...conv,\n                                        title\n                                    } : conv\n                            }[\"ConversationProvider.useCallback[updateConversationTitle]\"])\n                    }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    // 更新全局缓存\n                    globalConversationsCache = globalConversationsCache.map({\n                        \"ConversationProvider.useCallback[updateConversationTitle]\": (conv)=>conv.id === id ? {\n                                ...conv,\n                                title\n                            } : conv\n                    }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    // 如果是当前对话，也更新当前对话状态\n                    if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === id) {\n                        setCurrentConversation({\n                            \"ConversationProvider.useCallback[updateConversationTitle]\": (prev)=>prev ? {\n                                    ...prev,\n                                    title\n                                } : null\n                        }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    }\n                } else {\n                    console.error(\"[ConversationProvider] 更新对话 \".concat(id, \" 标题失败:\"), data.error);\n                    setError(data.error || '更新对话标题失败');\n                    throw new Error(data.error || '更新对话标题失败');\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 更新对话 \".concat(id, \" 标题异常:\"), err);\n                setError('网络错误，更新对话标题失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[updateConversationTitle]\"], [\n        currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id\n    ]);\n    // 初始化时加载对话列表 - 修复：移除loadConversations依赖避免无限循环\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationProvider.useEffect\": ()=>{\n            loadConversations();\n        }\n    }[\"ConversationProvider.useEffect\"], []); // 只在组件挂载时执行一次\n    // 组件卸载时清理 - 优化：清理所有请求和缓存\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationProvider.useEffect\": ()=>{\n            return ({\n                \"ConversationProvider.useEffect\": ()=>{\n                    console.log('[ConversationProvider] 组件卸载，清理资源...');\n                    // 取消当前的AbortController\n                    if (abortControllerRef.current) {\n                        abortControllerRef.current.abort();\n                    }\n                    // 暂时移除API请求管理器的调用\n                    // 重置全局状态\n                    isLoadingGlobal = false;\n                }\n            })[\"ConversationProvider.useEffect\"];\n        }\n    }[\"ConversationProvider.useEffect\"], []);\n    const value = {\n        conversations,\n        currentConversation,\n        loading,\n        error,\n        loadConversations,\n        createConversation,\n        switchConversation,\n        deleteConversation,\n        updateConversationTitle,\n        setCurrentConversation\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConversationContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\contexts\\\\ConversationContext.tsx\",\n        lineNumber: 295,\n        columnNumber: 5\n    }, this);\n}\n_s(ConversationProvider, \"eDx1BKB9zBJkVZ1FPc7VJppvBuo=\");\n_c = ConversationProvider;\nfunction useConversations() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ConversationContext);\n    if (context === undefined) {\n        throw new Error('useConversations must be used within a ConversationProvider');\n    }\n    return context;\n}\n_s1(useConversations, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ConversationProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ConversationContext.tsx\n"));

/***/ })

});