"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/agents/page",{

/***/ "(app-pages-browser)/./src/app/Sidebar.tsx":
/*!*****************************!*\
  !*** ./src/app/Sidebar.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-right.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/workflow.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _theme_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/theme/components/ThemeToggle */ \"(app-pages-browser)/./src/theme/components/ThemeToggle.tsx\");\n/* harmony import */ var _theme_components_ColorThemeSwitcher__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/theme/components/ColorThemeSwitcher */ \"(app-pages-browser)/./src/theme/components/ColorThemeSwitcher.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n// 简化的侧边栏状态管理\nfunction useSidebarState() {\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const toggleSidebar = ()=>{\n        const newState = !isExpanded;\n        setIsExpanded(newState);\n        if (true) {\n            document.documentElement.setAttribute('data-sidebar-state', newState ? 'expanded' : 'collapsed');\n            localStorage.setItem('sidebar-expanded', JSON.stringify(newState));\n        }\n    };\n    // 在客户端挂载后同步真实状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useSidebarState.useEffect\": ()=>{\n            if (true) {\n                const state = document.documentElement.getAttribute('data-sidebar-state');\n                const actualState = state === 'expanded';\n                if (actualState !== isExpanded) {\n                    setIsExpanded(actualState);\n                }\n            }\n        }\n    }[\"useSidebarState.useEffect\"], [\n        isExpanded\n    ]);\n    return {\n        isExpanded,\n        toggleSidebar\n    };\n}\n_s(useSidebarState, \"VGKo2U2z4tCZFJeh2IZSoSqn0qE=\");\nfunction Sidebar(param) {\n    let { conversations, currentConversation, onCreateConversation, onLoadConversation, onDeleteConversation } = param;\n    _s1();\n    const { isExpanded, toggleSidebar } = useSidebarState();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    // 处理创建新对话的逻辑\n    const handleNewConversation = ()=>{\n        if (pathname === '/simple-chat') {\n            onCreateConversation();\n        } else {\n            router.push('/simple-chat?new=true');\n        }\n    };\n    // 处理进入聊天页面的逻辑\n    const handleGoToChat = ()=>{\n        const hasConversations = conversations && conversations.length > 0;\n        if (pathname === '/simple-chat') {\n            if (hasConversations && !currentConversation) {\n                const latestConversation = conversations.sort((a, b)=>new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())[0];\n                onLoadConversation(latestConversation.id);\n            } else if (!hasConversations) {\n                onCreateConversation();\n            }\n        } else {\n            if (hasConversations) {\n                const latestConversation = conversations.sort((a, b)=>new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())[0];\n                router.push(\"/simple-chat?id=\".concat(latestConversation.id));\n            } else {\n                router.push('/simple-chat?new=true');\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sidebar-container relative bg-theme-card border-r border-theme-border flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"group p-4 border-b border-theme-border flex items-center justify-center relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/\",\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"/assets/<EMAIL>\",\n                                alt: \"Kun Agent Logo\",\n                                className: \"w-8 h-8 transition-all duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"sidebar-text text-lg font-bold text-theme-foreground ml-2\",\n                                children: \"Kun Agent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleSidebar,\n                        className: \"p-1.5 rounded-full text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-card-hover transition-all duration-300 opacity-0 group-hover:opacity-100 absolute right-0 translate-x-1/2 top-1/2 -translate-y-1/2 bg-theme-card border border-theme-border shadow-sm\",\n                        children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleNewConversation,\n                    className: \"sidebar-button group relative w-full flex items-center gap-3 p-3 rounded-lg bg-theme-primary text-white hover:bg-theme-primary-hover transition-colors duration-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-5 h-5 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sidebar-text text-sm font-semibold\",\n                            children: \"新建对话\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                            children: \"新建对话\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pl-2 flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/\",\n                            className: \"sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 \".concat(pathname === '/' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"首页\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"首页\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/mcp-config\",\n                            className: \"sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 \".concat(pathname === '/mcp-config' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"MCP配置\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"MCP配置\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/model-manager\",\n                            className: \"sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 \".concat(pathname === '/model-manager' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"模型管理\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"模型管理\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/agents\",\n                            className: \"sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 \".concat(pathname === '/agents' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"智能体管理\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"智能体管理\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleGoToChat,\n                            className: \"sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 w-full text-left \".concat(pathname === '/simple-chat' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"AI聊天\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: conversations.length > 0 ? \"进入最新对话\" : \"开始AI聊天\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-theme-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/conversations\",\n                                className: \"sidebar-button sidebar-nav-item group relative w-full flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 \".concat(pathname === '/conversations' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-card-hover'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sidebar-icon-container\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sidebar-text text-sm\",\n                                        children: \"对话历史\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                        children: \"对话历史\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/settings\",\n                                className: \"sidebar-button sidebar-nav-item group relative w-full flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 \".concat(pathname === '/settings' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-card-hover'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sidebar-icon-container\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-5 h-5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sidebar-text text-sm\",\n                                        children: \"设置\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                        children: \"设置\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 pb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sidebar-button group relative w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 hover:bg-theme-card-hover\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container theme-toggle-icon\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__.ThemeToggle, {\n                                            variant: \"icon\",\n                                            size: \"sm\",\n                                            className: \"sidebar-collapsed-only\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this),\n                                        isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_components_ColorThemeSwitcher__WEBPACK_IMPORTED_MODULE_3__.ColorThemeSwitcher, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 30\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm text-theme-foreground-muted flex-1\",\n                                    children: \"外观主题\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-text\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__.ThemeToggle, {\n                                        variant: \"icon\",\n                                        size: \"sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"切换主题\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n_s1(Sidebar, \"RGDR0SOld2As14DPjtpKqOHL8aI=\", false, function() {\n    return [\n        useSidebarState,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/Sidebar.tsx\n"));

/***/ })

});