"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mcp-config/page",{

/***/ "(app-pages-browser)/./src/app/mcp-config/page.tsx":
/*!*************************************!*\
  !*** ./src/app/mcp-config/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ McpConfigPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _hooks_useMcpConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hooks/useMcpConfig */ \"(app-pages-browser)/./src/app/mcp-config/hooks/useMcpConfig.ts\");\n/* harmony import */ var _components_AddServerModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/AddServerModal */ \"(app-pages-browser)/./src/app/mcp-config/components/AddServerModal.tsx\");\n/* harmony import */ var _components_ToolsModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ToolsModal */ \"(app-pages-browser)/./src/app/mcp-config/components/ToolsModal.tsx\");\n/* harmony import */ var _barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Axe,Plus,RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Axe,Plus,RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Axe,Plus,RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Axe,Plus,RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/axe.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_notification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/notification */ \"(app-pages-browser)/./src/components/notification/index.ts\");\n/* harmony import */ var _components_Modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Modal */ \"(app-pages-browser)/./src/components/Modal.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../Sidebar */ \"(app-pages-browser)/./src/app/Sidebar.tsx\");\n/* harmony import */ var _components_Loading__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Loading */ \"(app-pages-browser)/./src/components/Loading.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction McpConfigPage() {\n    _s();\n    const { servers, tools, loading, toolsLoading, showAddModal, showToolsModal, selectedTab, selectedServer, newServer, setShowAddModal, setShowToolsModal, setNewServer, setTools, loadServers, loadTools, handleTabChange, handleServerSelect, checkServerStatus, refreshTools, handleDeleteTool, handleUseTool, handleAddServer, handleDeleteServer, executionResult, setExecutionResult, usingToolId } = (0,_hooks_useMcpConfig__WEBPACK_IMPORTED_MODULE_1__.useMcpConfig)();\n    // 操作类弹窗\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [serverToDelete, setServerToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    var _useNotification;\n    const notification = (_useNotification = _components_notification__WEBPACK_IMPORTED_MODULE_5__.useNotification === null || _components_notification__WEBPACK_IMPORTED_MODULE_5__.useNotification === void 0 ? void 0 : (0,_components_notification__WEBPACK_IMPORTED_MODULE_5__.useNotification)()) !== null && _useNotification !== void 0 ? _useNotification : null;\n    // 移除本地对话状态，改为使用全局状态\n    // 侧边栏事件处理\n    const handleCreateConversation = ()=>{\n        window.location.href = '/simple-chat?new=true';\n    };\n    const handleLoadConversation = (conversationId)=>{\n        window.location.href = \"/simple-chat?id=\".concat(conversationId);\n    };\n    const handleDeleteConversation = async (conversationId)=>{\n    // 删除逻辑移到全局状态管理中\n    };\n    // 删除服务器弹窗触发\n    const handleDeleteServerModal = (server)=>{\n        setServerToDelete(server);\n        setDeleteModalOpen(true);\n    };\n    // 确认删除服务器\n    const confirmDeleteServer = async ()=>{\n        if (!serverToDelete) return;\n        try {\n            // 本地服务器不允许删除\n            if (serverToDelete.name === 'local') {\n                var _notification_error;\n                notification && ((_notification_error = notification.error) === null || _notification_error === void 0 ? void 0 : _notification_error.call(notification, '本地服务器不支持删除操作'));\n                setDeleteModalOpen(false);\n                setServerToDelete(null);\n                return;\n            }\n            // 先删除数据库记录\n            const response = await fetch(\"/api/mcp/servers/\".concat(serverToDelete.id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                // 然后删除配置文件中的记录\n                const configResponse = await fetch('/api/mcp/config', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'delete',\n                        serverName: serverToDelete.name\n                    })\n                });\n                if (configResponse.ok) {\n                    var _notification_success;\n                    notification && ((_notification_success = notification.success) === null || _notification_success === void 0 ? void 0 : _notification_success.call(notification, '服务器删除成功', \"服务器「\".concat(serverToDelete.displayName || serverToDelete.name, \"」已删除\")));\n                    if (selectedServer === serverToDelete.name) {\n                        handleServerSelect(null);\n                        setTools([]);\n                    }\n                    await loadServers();\n                } else {\n                    var _notification_error1;\n                    const configErrorData = await configResponse.json();\n                    notification && ((_notification_error1 = notification.error) === null || _notification_error1 === void 0 ? void 0 : _notification_error1.call(notification, '删除配置文件失败', configErrorData.error || '未知错误'));\n                }\n            } else {\n                var _notification_error2;\n                const errorData = await response.json();\n                notification && ((_notification_error2 = notification.error) === null || _notification_error2 === void 0 ? void 0 : _notification_error2.call(notification, '删除服务器失败', errorData.error || '未知错误'));\n            }\n        } catch (error) {\n            var _notification_error3;\n            notification && ((_notification_error3 = notification.error) === null || _notification_error3 === void 0 ? void 0 : _notification_error3.call(notification, '删除服务器失败', error instanceof Error ? error.message : '未知错误'));\n        } finally{\n            setDeleteModalOpen(false);\n            setServerToDelete(null);\n        }\n    };\n    const statusClasses = {\n        connected: 'bg-theme-success',\n        error: 'bg-theme-error',\n        disconnected: 'bg-theme-foreground-muted',\n        connecting: 'bg-yellow-500'\n    };\n    const borderClasses = {\n        connecting: 'ring-2 ring-yellow-500/50 ring-offset-2 ring-offset-theme-background-secondary animate-pulse',\n        selected: 'ring-2 ring-theme-primary ring-offset-2 ring-offset-theme-background-secondary',\n        default: 'border-theme-border hover:border-theme-border-secondary'\n    };\n    const getBorderClass = (server)=>{\n        if (server.status === 'connecting') return borderClasses.connecting;\n        if (selectedServer === server.name) return borderClasses.selected;\n        return borderClasses.default;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notification__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-screen bg-theme-background-secondary\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_7__.Sidebar, {\n                        onCreateConversation: handleCreateConversation,\n                        onLoadConversation: handleLoadConversation,\n                        onDeleteConversation: handleDeleteConversation\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loading__WEBPACK_IMPORTED_MODULE_8__.PageLoading, {\n                            text: \"正在加载MCP服务器配置...\",\n                            fullScreen: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notification__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-theme-background-secondary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_7__.Sidebar, {\n                    onCreateConversation: handleCreateConversation,\n                    onLoadConversation: handleLoadConversation,\n                    onDeleteConversation: handleDeleteConversation\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-auto scrollbar-thin\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen bg-theme-background-secondary transition-colors duration-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notification__WEBPACK_IMPORTED_MODULE_5__.NotificationManager, {\n                                position: \"top-right\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"max-w-7xl mx-auto py-8 sm:px-6 lg:px-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-6 sm:px-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"page-title\",\n                                                                children: \"\\uD83C\\uDF10MCP 服务器配置\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"page-subtitle mt-2\",\n                                                                children: \"管理模型上下文协议服务器，配置工具和连接\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowAddModal(true),\n                                                            className: \"inline-flex items-center gap-2 px-4 py-2 bg-theme-primary text-white rounded-lg hover:bg-theme-primary-hover transition-colors duration-200 font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"添加服务器\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-b border-theme-border\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                    className: \"-mb-px flex space-x-8\",\n                                                    children: [\n                                                        {\n                                                            key: 'all',\n                                                            label: '全部',\n                                                            count: servers.length\n                                                        },\n                                                        {\n                                                            key: 'local',\n                                                            label: '本地服务器',\n                                                            count: servers.filter((s)=>s.type === 'stdio').length\n                                                        },\n                                                        {\n                                                            key: 'external',\n                                                            label: '外部服务器',\n                                                            count: servers.filter((s)=>s.type !== 'stdio').length\n                                                        }\n                                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleTabChange(tab.key),\n                                                            className: \"\".concat(selectedTab === tab.key ? 'border-theme-primary text-theme-primary' : 'border-transparent text-theme-foreground-muted hover:text-theme-foreground hover:border-theme-border-secondary', \" whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors duration-200 focus:outline-none\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tab.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-theme-background-tertiary text-theme-foreground-secondary py-0.5 px-2.5 rounded-full text-xs font-medium\",\n                                                                    children: tab.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, tab.key, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                    children: servers.filter((server)=>{\n                                                        if (selectedTab === 'all') return true;\n                                                        if (selectedTab === 'local') return server.type === 'stdio';\n                                                        if (selectedTab === 'external') return server.type !== 'stdio';\n                                                        return true;\n                                                    }).map((server)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-theme-card border rounded-lg p-4 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 \".concat(getBorderClass(server)),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start justify-between mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-base font-semibold text-theme-foreground truncate\",\n                                                                                children: server.displayName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                lineNumber: 247,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-1 pl-4\",\n                                                                            children: [\n                                                                                server.type !== 'stdio' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        checkServerStatus(server.name);\n                                                                                    },\n                                                                                    className: \"text-theme-foreground-muted hover:text-theme-primary p-1.5 rounded-full hover:bg-theme-primary/10 transition-colors\",\n                                                                                    title: \"检查连接状态\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"w-3.5 h-3.5 \".concat(server.status === 'connecting' ? 'animate-spin' : '')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                        lineNumber: 259,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                    lineNumber: 251,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                server.name !== 'local' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        handleDeleteServerModal(server);\n                                                                                    },\n                                                                                    className: \"text-theme-foreground-muted hover:text-theme-error p-1.5 rounded-full hover:bg-theme-error/10 transition-colors\",\n                                                                                    title: \"删除服务器\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                        className: \"w-3.5 h-3.5\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                        lineNumber: 271,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                    lineNumber: 263,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                            lineNumber: 249,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-theme-foreground-muted my-3 h-10 overflow-hidden\",\n                                                                    children: server.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                server.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-theme-error mb-2 truncate\",\n                                                                    title: server.errorMessage,\n                                                                    children: [\n                                                                        \"错误: \",\n                                                                        server.errorMessage\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-sm mt-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2.5 h-2.5 rounded-full \".concat(statusClasses[server.status]),\n                                                                                    title: \"状态: \".concat(server.status)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                    lineNumber: 284,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-theme-foreground-muted capitalize\",\n                                                                                    children: server.type\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                    lineNumber: 285,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                            lineNumber: 283,\n                                                                            columnNumber: 30\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    handleServerSelect(server.name);\n                                                                                },\n                                                                                className: \"bg-theme-primary/10 hover:bg-theme-primary/20 text-theme-primary px-3 py-1.5 rounded-full text-xs font-semibold flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                        lineNumber: 295,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            server.toolCount || 0,\n                                                                                            \" 工具\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                        lineNumber: 296,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                lineNumber: 288,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, server.name, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddServerModal__WEBPACK_IMPORTED_MODULE_2__.AddServerModal, {\n                                isOpen: showAddModal,\n                                onClose: ()=>setShowAddModal(false),\n                                newServer: newServer,\n                                onServerChange: setNewServer,\n                                onSubmit: handleAddServer\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ToolsModal__WEBPACK_IMPORTED_MODULE_3__.ToolsModal, {\n                                isOpen: showToolsModal,\n                                onClose: ()=>setShowToolsModal(false),\n                                serverName: selectedServer || '',\n                                tools: tools,\n                                onUseTool: handleUseTool,\n                                usingToolId: usingToolId,\n                                onToolUpdate: (updatedTool)=>{\n                                    // 更新工具列表中的对应工具\n                                    setTools((prevTools)=>prevTools.map((tool)=>tool.id === updatedTool.id ? updatedTool : tool));\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this),\n                            executionResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-theme-card rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-auto scrollbar-thin\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-theme-foreground\",\n                                                    children: [\n                                                        \"工具执行结果 - \",\n                                                        executionResult.toolName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setExecutionResult(null),\n                                                    className: \"text-theme-foreground-muted hover:text-theme-foreground\",\n                                                    children: \"✕\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 19\n                                        }, this),\n                                        executionResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-theme-success\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"执行成功\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-theme-background-secondary rounded-md p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"text-sm text-theme-foreground whitespace-pre-wrap\",\n                                                        children: JSON.stringify(executionResult.data, null, 2)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-theme-error\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"执行失败\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-theme-error/10 rounded-md p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-theme-error\",\n                                                        children: executionResult.error\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                open: deleteModalOpen,\n                                onClose: ()=>{\n                                    setDeleteModalOpen(false);\n                                    setServerToDelete(null);\n                                },\n                                title: \"确认删除服务器\",\n                                actions: [\n                                    {\n                                        label: '取消',\n                                        onClick: ()=>{\n                                            setDeleteModalOpen(false);\n                                            setServerToDelete(null);\n                                        },\n                                        variant: 'secondary'\n                                    },\n                                    {\n                                        label: '确认删除',\n                                        onClick: confirmDeleteServer,\n                                        variant: 'danger',\n                                        autoFocus: true\n                                    }\n                                ],\n                                width: 380,\n                                children: serverToDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"确定要删除服务器「\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                            children: serverToDelete.displayName || serverToDelete.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 28\n                                        }, this),\n                                        \"」吗？此操作不可撤销。\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(McpConfigPage, \"lI7oBw7KvjVAC6akDiOxF5NCynk=\", false, function() {\n    return [\n        _hooks_useMcpConfig__WEBPACK_IMPORTED_MODULE_1__.useMcpConfig\n    ];\n});\n_c = McpConfigPage;\nvar _c;\n$RefreshReg$(_c, \"McpConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/mcp-config/page.tsx\n"));

/***/ })

});