"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/ConversationContext.tsx":
/*!**********************************************!*\
  !*** ./src/contexts/ConversationContext.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationProvider: () => (/* binding */ ConversationProvider),\n/* harmony export */   useConversations: () => (/* binding */ useConversations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ConversationProvider,useConversations auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ConversationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// 简化的全局缓存配置\nconst CACHE_DURATION = 30000; // 30秒缓存\nlet globalConversationsCache = [];\nlet globalCacheTimestamp = 0;\nlet isLoadingGlobal = false;\nfunction ConversationProvider(param) {\n    let { children } = param;\n    _s();\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 防止重复请求的引用\n    const loadingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 检查缓存是否有效\n    const isCacheValid = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[isCacheValid]\": ()=>{\n            const now = Date.now();\n            return globalConversationsCache.length > 0 && now - globalCacheTimestamp < CACHE_DURATION;\n        }\n    }[\"ConversationProvider.useCallback[isCacheValid]\"], []);\n    // 加载对话列表 - 简化版本，添加基本缓存\n    const loadConversations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[loadConversations]\": async function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n            // 如果正在加载中，直接返回\n            if (loadingRef.current || isLoadingGlobal) {\n                console.log('[ConversationProvider] 已有加载进行中，跳过');\n                return;\n            }\n            // 如果不强制刷新且缓存有效，使用缓存\n            if (!forceRefresh && isCacheValid()) {\n                console.log('[ConversationProvider] 使用缓存的对话列表');\n                setConversations(globalConversationsCache);\n                return;\n            }\n            try {\n                loadingRef.current = true;\n                isLoadingGlobal = true;\n                setLoading(true);\n                setError(null);\n                // 取消之前的请求\n                if (abortControllerRef.current) {\n                    abortControllerRef.current.abort();\n                }\n                // 创建新的AbortController\n                abortControllerRef.current = new AbortController();\n                console.log('[ConversationProvider] 开始加载对话列表...');\n                // 暂时使用原生fetch避免潜在的循环问题\n                const response = await fetch('/api/conversations', {\n                    signal: abortControllerRef.current.signal\n                });\n                const data = await response.json();\n                if (data.success) {\n                    const conversationList = data.conversations || [];\n                    // 更新全局缓存\n                    globalConversationsCache = conversationList;\n                    globalCacheTimestamp = Date.now();\n                    setConversations(conversationList);\n                    console.log(\"[ConversationProvider] 成功加载 \".concat(conversationList.length, \" 个对话\"));\n                } else {\n                    setError(data.error || '加载对话列表失败');\n                }\n            } catch (err) {\n                if (err instanceof Error && err.name === 'AbortError') {\n                    console.log('[ConversationProvider] 请求被取消');\n                    return;\n                }\n                setError('网络错误，加载对话列表失败');\n                console.error('加载对话列表失败:', err);\n            } finally{\n                loadingRef.current = false;\n                isLoadingGlobal = false;\n                setLoading(false);\n                abortControllerRef.current = null;\n            }\n        }\n    }[\"ConversationProvider.useCallback[loadConversations]\"], [\n        isCacheValid\n    ]);\n    // 创建新对话\n    const createConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[createConversation]\": async (title, model)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始创建对话: \".concat(title, \", 模型: \").concat(model));\n                const response = await fetch('/api/conversations', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        title,\n                        model\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 对话创建成功:\", data.conversation);\n                    // 刷新对话列表\n                    await loadConversations(true);\n                    // 切换到新创建的对话\n                    setCurrentConversation(data.conversation);\n                    return data.conversation.id;\n                } else {\n                    console.error(\"[ConversationProvider] 创建对话失败:\", data.error);\n                    setError(data.error || '创建对话失败');\n                    return null;\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 创建对话异常:\", err);\n                setError('网络错误，创建对话失败');\n                return null;\n            }\n        }\n    }[\"ConversationProvider.useCallback[createConversation]\"], [\n        loadConversations\n    ]);\n    // 切换对话\n    const switchConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[switchConversation]\": async (id)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始切换到对话 \".concat(id));\n                const response = await fetch(\"/api/conversations/\".concat(id));\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 成功获取对话 \".concat(id, \" 信息:\"), data.conversation);\n                    setCurrentConversation(data.conversation);\n                } else {\n                    console.error(\"[ConversationProvider] 切换对话 \".concat(id, \" 失败:\"), data.error);\n                    setError(data.error || '切换对话失败');\n                    throw new Error(data.error || '切换对话失败');\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 切换对话 \".concat(id, \" 异常:\"), err);\n                setError('网络错误，切换对话失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[switchConversation]\"], []);\n    // 删除对话\n    const deleteConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[deleteConversation]\": async (id)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始删除对话 \".concat(id));\n                const response = await fetch(\"/api/conversations/\".concat(id), {\n                    method: 'DELETE'\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 对话 \".concat(id, \" 删除成功\"));\n                    // 从本地状态中移除\n                    setConversations({\n                        \"ConversationProvider.useCallback[deleteConversation]\": (prev)=>prev.filter({\n                                \"ConversationProvider.useCallback[deleteConversation]\": (conv)=>conv.id !== id\n                            }[\"ConversationProvider.useCallback[deleteConversation]\"])\n                    }[\"ConversationProvider.useCallback[deleteConversation]\"]);\n                    // 更新全局缓存\n                    globalConversationsCache = globalConversationsCache.filter({\n                        \"ConversationProvider.useCallback[deleteConversation]\": (conv)=>conv.id !== id\n                    }[\"ConversationProvider.useCallback[deleteConversation]\"]);\n                    // 如果删除的是当前对话，清空当前对话\n                    if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === id) {\n                        setCurrentConversation(null);\n                    }\n                } else {\n                    console.error(\"[ConversationProvider] 删除对话 \".concat(id, \" 失败:\"), data.error);\n                    setError(data.error || '删除对话失败');\n                    throw new Error(data.error || '删除对话失败');\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 删除对话 \".concat(id, \" 异常:\"), err);\n                setError('网络错误，删除对话失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[deleteConversation]\"], [\n        currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id\n    ]);\n    // 更新对话标题\n    const updateConversationTitle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[updateConversationTitle]\": async (id, title)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始更新对话 \".concat(id, \" 标题: \").concat(title));\n                const response = await fetch(\"/api/conversations/\".concat(id), {\n                    method: 'PATCH',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        title\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 对话 \".concat(id, \" 标题更新成功\"));\n                    // 更新本地状态\n                    setConversations({\n                        \"ConversationProvider.useCallback[updateConversationTitle]\": (prev)=>prev.map({\n                                \"ConversationProvider.useCallback[updateConversationTitle]\": (conv)=>conv.id === id ? {\n                                        ...conv,\n                                        title\n                                    } : conv\n                            }[\"ConversationProvider.useCallback[updateConversationTitle]\"])\n                    }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    // 更新全局缓存\n                    globalConversationsCache = globalConversationsCache.map({\n                        \"ConversationProvider.useCallback[updateConversationTitle]\": (conv)=>conv.id === id ? {\n                                ...conv,\n                                title\n                            } : conv\n                    }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    // 如果是当前对话，也更新当前对话状态\n                    if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === id) {\n                        setCurrentConversation({\n                            \"ConversationProvider.useCallback[updateConversationTitle]\": (prev)=>prev ? {\n                                    ...prev,\n                                    title\n                                } : null\n                        }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    }\n                } else {\n                    console.error(\"[ConversationProvider] 更新对话 \".concat(id, \" 标题失败:\"), data.error);\n                    setError(data.error || '更新对话标题失败');\n                    throw new Error(data.error || '更新对话标题失败');\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 更新对话 \".concat(id, \" 标题异常:\"), err);\n                setError('网络错误，更新对话标题失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[updateConversationTitle]\"], [\n        currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id\n    ]);\n    // 初始化时加载对话列表 - 修复：移除loadConversations依赖避免无限循环\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationProvider.useEffect\": ()=>{\n            loadConversations();\n        }\n    }[\"ConversationProvider.useEffect\"], []); // 只在组件挂载时执行一次\n    // 组件卸载时清理 - 优化：清理所有请求和缓存\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationProvider.useEffect\": ()=>{\n            return ({\n                \"ConversationProvider.useEffect\": ()=>{\n                    console.log('[ConversationProvider] 组件卸载，清理资源...');\n                    // 取消当前的AbortController\n                    if (abortControllerRef.current) {\n                        abortControllerRef.current.abort();\n                    }\n                    // 暂时移除API请求管理器的调用\n                    // 重置全局状态\n                    isLoadingGlobal = false;\n                }\n            })[\"ConversationProvider.useEffect\"];\n        }\n    }[\"ConversationProvider.useEffect\"], []);\n    const value = {\n        conversations,\n        currentConversation,\n        loading,\n        error,\n        loadConversations,\n        createConversation,\n        switchConversation,\n        deleteConversation,\n        updateConversationTitle,\n        setCurrentConversation\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConversationContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\contexts\\\\ConversationContext.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, this);\n}\n_s(ConversationProvider, \"eDx1BKB9zBJkVZ1FPc7VJppvBuo=\");\n_c = ConversationProvider;\nfunction useConversations() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ConversationContext);\n    if (context === undefined) {\n        throw new Error('useConversations must be used within a ConversationProvider');\n    }\n    return context;\n}\n_s1(useConversations, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ConversationProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ConversationContext.tsx\n"));

/***/ })

});