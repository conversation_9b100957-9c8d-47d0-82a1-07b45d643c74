globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/agents/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/ApiTrackerInit.tsx":{"*":{"id":"(ssr)/./src/components/ApiTrackerInit.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/notification/examples/NotificationDemo.tsx":{"*":{"id":"(ssr)/./src/components/notification/examples/NotificationDemo.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/notification/NotificationContainer.tsx":{"*":{"id":"(ssr)/./src/components/notification/NotificationContainer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/notification/NotificationContext.tsx":{"*":{"id":"(ssr)/./src/components/notification/NotificationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/notification/NotificationItem.tsx":{"*":{"id":"(ssr)/./src/components/notification/NotificationItem.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/notification/NotificationManager.tsx":{"*":{"id":"(ssr)/./src/components/notification/NotificationManager.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/RouteCleanupProvider.tsx":{"*":{"id":"(ssr)/./src/components/RouteCleanupProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/ConversationContext.tsx":{"*":{"id":"(ssr)/./src/contexts/ConversationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/theme/components/ColorThemeScript.tsx":{"*":{"id":"(ssr)/./src/theme/components/ColorThemeScript.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/theme/contexts/ThemeContext.tsx":{"*":{"id":"(ssr)/./src/theme/contexts/ThemeContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/mcp-config/page.tsx":{"*":{"id":"(ssr)/./src/app/mcp-config/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/model-manager/page.tsx":{"*":{"id":"(ssr)/./src/app/model-manager/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/agents/page.tsx":{"*":{"id":"(ssr)/./src/app/agents/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\ApiTrackerInit.tsx":{"id":"(app-pages-browser)/./src/components/ApiTrackerInit.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\examples\\NotificationDemo.tsx":{"id":"(app-pages-browser)/./src/components/notification/examples/NotificationDemo.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationContainer.tsx":{"id":"(app-pages-browser)/./src/components/notification/NotificationContainer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationContext.tsx":{"id":"(app-pages-browser)/./src/components/notification/NotificationContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationItem.tsx":{"id":"(app-pages-browser)/./src/components/notification/NotificationItem.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\notification\\NotificationManager.tsx":{"id":"(app-pages-browser)/./src/components/notification/NotificationManager.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\components\\RouteCleanupProvider.tsx":{"id":"(app-pages-browser)/./src/components/RouteCleanupProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\contexts\\ConversationContext.tsx":{"id":"(app-pages-browser)/./src/contexts/ConversationContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\components\\ColorThemeScript.tsx":{"id":"(app-pages-browser)/./src/theme/components/ColorThemeScript.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\theme\\contexts\\ThemeContext.tsx":{"id":"(app-pages-browser)/./src/theme/contexts/ThemeContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\page.tsx":{"id":"(app-pages-browser)/./src/app/mcp-config/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\model-manager\\page.tsx":{"id":"(app-pages-browser)/./src/app/model-manager/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\agents\\page.tsx":{"id":"(app-pages-browser)/./src/app/agents/page.tsx","name":"*","chunks":["app/agents/page","static/chunks/app/agents/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\":[],"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\agents\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ApiTrackerInit.tsx":{"*":{"id":"(rsc)/./src/components/ApiTrackerInit.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/notification/examples/NotificationDemo.tsx":{"*":{"id":"(rsc)/./src/components/notification/examples/NotificationDemo.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/notification/NotificationContainer.tsx":{"*":{"id":"(rsc)/./src/components/notification/NotificationContainer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/notification/NotificationContext.tsx":{"*":{"id":"(rsc)/./src/components/notification/NotificationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/notification/NotificationItem.tsx":{"*":{"id":"(rsc)/./src/components/notification/NotificationItem.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/notification/NotificationManager.tsx":{"*":{"id":"(rsc)/./src/components/notification/NotificationManager.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/RouteCleanupProvider.tsx":{"*":{"id":"(rsc)/./src/components/RouteCleanupProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/ConversationContext.tsx":{"*":{"id":"(rsc)/./src/contexts/ConversationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/theme/components/ColorThemeScript.tsx":{"*":{"id":"(rsc)/./src/theme/components/ColorThemeScript.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/theme/contexts/ThemeContext.tsx":{"*":{"id":"(rsc)/./src/theme/contexts/ThemeContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/mcp-config/page.tsx":{"*":{"id":"(rsc)/./src/app/mcp-config/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/model-manager/page.tsx":{"*":{"id":"(rsc)/./src/app/model-manager/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/agents/page.tsx":{"*":{"id":"(rsc)/./src/app/agents/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}