/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/mcp/server-list/route";
exports.ids = ["app/api/mcp/server-list/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Fserver-list%2Froute&page=%2Fapi%2Fmcp%2Fserver-list%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Fserver-list%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Fserver-list%2Froute&page=%2Fapi%2Fmcp%2Fserver-list%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Fserver-list%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_mcp_server_list_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/mcp/server-list/route.ts */ \"(rsc)/./src/app/api/mcp/server-list/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/mcp/server-list/route\",\n        pathname: \"/api/mcp/server-list\",\n        filename: \"route\",\n        bundlePath: \"app/api/mcp/server-list/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\api\\\\mcp\\\\server-list\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_mcp_server_list_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Fserver-list%2Froute&page=%2Fapi%2Fmcp%2Fserver-list%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Fserver-list%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/mcp/server-list/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/mcp/server-list/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(better_sqlite3__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst dbPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'chat.db');\n// 从数据库获取服务器列表和工具统计\nfunction getServersFromDatabase() {\n    const db = new (better_sqlite3__WEBPACK_IMPORTED_MODULE_1___default())(dbPath);\n    try {\n        // 获取服务器信息及其工具数量\n        const servers = db.prepare(`\n      SELECT \n        s.*,\n        COUNT(t.id) as tool_count\n      FROM mcp_servers s\n      LEFT JOIN mcp_tools t ON s.id = t.server_id AND t.is_available = 1 AND t.enabled = 1\n      WHERE s.enabled = 1\n      GROUP BY s.id\n      ORDER BY s.created_at DESC\n    `).all();\n        return servers;\n    } finally{\n        db.close();\n    }\n}\nasync function GET() {\n    try {\n        const servers = [];\n        // 添加本地服务器（从数据库获取工具数量）\n        try {\n            const { mcpServerClient } = __webpack_require__(/*! ../../../../lib/mcp/mcp-client-server */ \"(rsc)/./src/lib/mcp/mcp-client-server.ts\");\n            const isConnected = mcpServerClient.isClientConnected();\n            // 从数据库获取本地工具数量（如果有的话）\n            const db = new (better_sqlite3__WEBPACK_IMPORTED_MODULE_1___default())(dbPath);\n            let localToolCount = 0;\n            try {\n                const localServer = db.prepare('SELECT id FROM mcp_servers WHERE name = ?').get('local');\n                if (localServer) {\n                    const toolCountResult = db.prepare('SELECT COUNT(*) as count FROM mcp_tools WHERE server_id = ? AND is_available = 1 AND enabled = 1').get(localServer.id);\n                    localToolCount = toolCountResult?.count || 0;\n                } else {\n                    // 如果数据库中没有本地服务器记录，直接从客户端获取\n                    const localTools = isConnected ? mcpServerClient.getAvailableTools() : [];\n                    localToolCount = localTools.length;\n                }\n            } finally{\n                db.close();\n            }\n            servers.push({\n                name: 'local',\n                displayName: '本地服务器',\n                type: 'stdio',\n                status: isConnected ? 'connected' : 'disconnected',\n                toolCount: localToolCount,\n                description: '本地MCP服务器，提供基础工具功能'\n            });\n        } catch (error) {\n            console.error('获取本地服务器信息失败:', error);\n            servers.push({\n                name: 'local',\n                displayName: '本地服务器',\n                type: 'stdio',\n                status: 'error',\n                toolCount: 0,\n                description: '本地MCP服务器，提供基础工具功能'\n            });\n        }\n        // 从数据库获取外部服务器信息\n        try {\n            const dbServers = getServersFromDatabase();\n            // 添加数据库中的服务器信息（排除本地服务器，因为已经手动添加了）\n            dbServers.forEach((server)=>{\n                // 跳过本地服务器，避免重复\n                if (server.name === 'local') {\n                    return;\n                }\n                servers.push({\n                    id: server.id,\n                    name: server.name,\n                    displayName: server.display_name || server.name,\n                    type: server.type,\n                    status: server.status || 'disconnected',\n                    toolCount: server.tool_count || 0,\n                    description: server.description || `外部MCP服务器: ${server.name}`,\n                    lastConnectedAt: server.last_connected_at,\n                    errorMessage: server.error_message\n                });\n            });\n        } catch (error) {\n            console.error('获取外部服务器信息失败:', error);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            servers: servers\n        });\n    } catch (error) {\n        console.error('获取服务器列表失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: '获取服务器列表失败'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9tY3Avc2VydmVyLWxpc3Qvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTJDO0FBQ0w7QUFDZDtBQUV4QixNQUFNRyxTQUFTRCxnREFBUyxDQUFDRyxRQUFRQyxHQUFHLElBQUk7QUFjeEMsbUJBQW1CO0FBQ25CLFNBQVNDO0lBQ1AsTUFBTUMsS0FBSyxJQUFJUCx1REFBUUEsQ0FBQ0U7SUFFeEIsSUFBSTtRQUNGLGdCQUFnQjtRQUNoQixNQUFNTSxVQUFVRCxHQUFHRSxPQUFPLENBQUMsQ0FBQzs7Ozs7Ozs7O0lBUzVCLENBQUMsRUFBRUMsR0FBRztRQUVOLE9BQU9GO0lBQ1QsU0FBVTtRQUNSRCxHQUFHSSxLQUFLO0lBQ1Y7QUFDRjtBQWtCTyxlQUFlQztJQUNwQixJQUFJO1FBQ0YsTUFBTUosVUFBNEIsRUFBRTtRQUVwQyxzQkFBc0I7UUFDdEIsSUFBSTtZQUNGLE1BQU0sRUFBRUssZUFBZSxFQUFFLEdBQUdDLG1CQUFPQSxDQUFDLHVGQUF1QztZQUMzRSxNQUFNQyxjQUFjRixnQkFBZ0JHLGlCQUFpQjtZQUVyRCxzQkFBc0I7WUFDdEIsTUFBTVQsS0FBSyxJQUFJUCx1REFBUUEsQ0FBQ0U7WUFDeEIsSUFBSWUsaUJBQWlCO1lBQ3JCLElBQUk7Z0JBQ0YsTUFBTUMsY0FBY1gsR0FBR0UsT0FBTyxDQUFDLDZDQUE2Q1UsR0FBRyxDQUFDO2dCQUNoRixJQUFJRCxhQUFhO29CQUNmLE1BQU1FLGtCQUFrQmIsR0FBR0UsT0FBTyxDQUFDLG9HQUFvR1UsR0FBRyxDQUFDRCxZQUFZRyxFQUFFO29CQUN6SkosaUJBQWlCRyxpQkFBaUJFLFNBQVM7Z0JBQzdDLE9BQU87b0JBQ0wsMkJBQTJCO29CQUMzQixNQUFNQyxhQUFhUixjQUFjRixnQkFBZ0JXLGlCQUFpQixLQUFLLEVBQUU7b0JBQ3pFUCxpQkFBaUJNLFdBQVdFLE1BQU07Z0JBQ3BDO1lBQ0YsU0FBVTtnQkFDUmxCLEdBQUdJLEtBQUs7WUFDVjtZQUVBSCxRQUFRa0IsSUFBSSxDQUFDO2dCQUNYQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxNQUFNO2dCQUNOQyxRQUFRZixjQUFjLGNBQWM7Z0JBQ3BDZ0IsV0FBV2Q7Z0JBQ1hlLGFBQWE7WUFDZjtRQUNGLEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0JBQWdCQTtZQUM5QnpCLFFBQVFrQixJQUFJLENBQUM7Z0JBQ1hDLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE1BQU07Z0JBQ05DLFFBQVE7Z0JBQ1JDLFdBQVc7Z0JBQ1hDLGFBQWE7WUFDZjtRQUNGO1FBRUEsZ0JBQWdCO1FBQ2hCLElBQUk7WUFDRixNQUFNRyxZQUFZN0I7WUFFbEIsa0NBQWtDO1lBQ2xDNkIsVUFBVUMsT0FBTyxDQUFDLENBQUNDO2dCQUNqQixlQUFlO2dCQUNmLElBQUlBLE9BQU9WLElBQUksS0FBSyxTQUFTO29CQUMzQjtnQkFDRjtnQkFFQW5CLFFBQVFrQixJQUFJLENBQUM7b0JBQ1hMLElBQUlnQixPQUFPaEIsRUFBRTtvQkFDYk0sTUFBTVUsT0FBT1YsSUFBSTtvQkFDakJDLGFBQWFTLE9BQU9DLFlBQVksSUFBSUQsT0FBT1YsSUFBSTtvQkFDL0NFLE1BQU1RLE9BQU9SLElBQUk7b0JBQ2pCQyxRQUFRTyxPQUFPUCxNQUFNLElBQUk7b0JBQ3pCQyxXQUFXTSxPQUFPRSxVQUFVLElBQUk7b0JBQ2hDUCxhQUFhSyxPQUFPTCxXQUFXLElBQUksQ0FBQyxVQUFVLEVBQUVLLE9BQU9WLElBQUksRUFBRTtvQkFDN0RhLGlCQUFpQkgsT0FBT0ksaUJBQWlCO29CQUN6Q0MsY0FBY0wsT0FBT00sYUFBYTtnQkFDcEM7WUFDRjtRQUNGLEVBQUUsT0FBT1YsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0JBQWdCQTtRQUNoQztRQUVBLE9BQU9sQyxxREFBWUEsQ0FBQzZDLElBQUksQ0FBQztZQUN2QkMsU0FBUztZQUNUckMsU0FBU0E7UUFDWDtJQUNGLEVBQUUsT0FBT3lCLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLGNBQWNBO1FBQzVCLE9BQU9sQyxxREFBWUEsQ0FBQzZDLElBQUksQ0FDdEI7WUFBRVgsT0FBTztRQUFZLEdBQ3JCO1lBQUVILFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxzcmNcXGFwcFxcYXBpXFxtY3BcXHNlcnZlci1saXN0XFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgRGF0YWJhc2UgZnJvbSAnYmV0dGVyLXNxbGl0ZTMnO1xuaW1wb3J0IHBhdGggZnJvbSAncGF0aCc7XG5cbmNvbnN0IGRiUGF0aCA9IHBhdGguam9pbihwcm9jZXNzLmN3ZCgpLCAnY2hhdC5kYicpO1xuXG5pbnRlcmZhY2UgRGJTZXJ2ZXJSZXN1bHQge1xuICBpZDogbnVtYmVyO1xuICBuYW1lOiBzdHJpbmc7XG4gIGRpc3BsYXlfbmFtZTogc3RyaW5nO1xuICB0eXBlOiBzdHJpbmc7XG4gIHN0YXR1czogc3RyaW5nO1xuICBkZXNjcmlwdGlvbj86IHN0cmluZztcbiAgbGFzdF9jb25uZWN0ZWRfYXQ/OiBzdHJpbmc7XG4gIGVycm9yX21lc3NhZ2U/OiBzdHJpbmc7XG4gIHRvb2xfY291bnQ6IG51bWJlcjtcbn1cblxuLy8g5LuO5pWw5o2u5bqT6I635Y+W5pyN5Yqh5Zmo5YiX6KGo5ZKM5bel5YW357uf6K6hXG5mdW5jdGlvbiBnZXRTZXJ2ZXJzRnJvbURhdGFiYXNlKCk6IERiU2VydmVyUmVzdWx0W10ge1xuICBjb25zdCBkYiA9IG5ldyBEYXRhYmFzZShkYlBhdGgpO1xuICBcbiAgdHJ5IHtcbiAgICAvLyDojrflj5bmnI3liqHlmajkv6Hmga/lj4rlhbblt6XlhbfmlbDph49cbiAgICBjb25zdCBzZXJ2ZXJzID0gZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgXG4gICAgICAgIHMuKixcbiAgICAgICAgQ09VTlQodC5pZCkgYXMgdG9vbF9jb3VudFxuICAgICAgRlJPTSBtY3Bfc2VydmVycyBzXG4gICAgICBMRUZUIEpPSU4gbWNwX3Rvb2xzIHQgT04gcy5pZCA9IHQuc2VydmVyX2lkIEFORCB0LmlzX2F2YWlsYWJsZSA9IDEgQU5EIHQuZW5hYmxlZCA9IDFcbiAgICAgIFdIRVJFIHMuZW5hYmxlZCA9IDFcbiAgICAgIEdST1VQIEJZIHMuaWRcbiAgICAgIE9SREVSIEJZIHMuY3JlYXRlZF9hdCBERVNDXG4gICAgYCkuYWxsKCkgYXMgRGJTZXJ2ZXJSZXN1bHRbXTtcbiAgICBcbiAgICByZXR1cm4gc2VydmVycztcbiAgfSBmaW5hbGx5IHtcbiAgICBkYi5jbG9zZSgpO1xuICB9XG59XG5cbi8qKlxuICog6I635Y+WTUNQ5pyN5Yqh5Zmo5YiX6KGoQVBJXG4gKiBHRVQgL2FwaS9tY3Avc2VydmVyLWxpc3RcbiAqL1xuaW50ZXJmYWNlIFNlcnZlclJlc3BvbnNlIHtcbiAgaWQ/OiBudW1iZXI7XG4gIG5hbWU6IHN0cmluZztcbiAgZGlzcGxheU5hbWU6IHN0cmluZztcbiAgdHlwZTogc3RyaW5nO1xuICBzdGF0dXM6IHN0cmluZztcbiAgdG9vbENvdW50OiBudW1iZXI7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIGxhc3RDb25uZWN0ZWRBdD86IHN0cmluZztcbiAgZXJyb3JNZXNzYWdlPzogc3RyaW5nO1xufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKCkge1xuICB0cnkge1xuICAgIGNvbnN0IHNlcnZlcnM6IFNlcnZlclJlc3BvbnNlW10gPSBbXTtcbiAgICBcbiAgICAvLyDmt7vliqDmnKzlnLDmnI3liqHlmajvvIjku47mlbDmja7lupPojrflj5blt6XlhbfmlbDph4/vvIlcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBtY3BTZXJ2ZXJDbGllbnQgfSA9IHJlcXVpcmUoJy4uLy4uLy4uLy4uL2xpYi9tY3AvbWNwLWNsaWVudC1zZXJ2ZXInKTtcbiAgICAgIGNvbnN0IGlzQ29ubmVjdGVkID0gbWNwU2VydmVyQ2xpZW50LmlzQ2xpZW50Q29ubmVjdGVkKCk7XG4gICAgICBcbiAgICAgIC8vIOS7juaVsOaNruW6k+iOt+WPluacrOWcsOW3peWFt+aVsOmHj++8iOWmguaenOacieeahOivne+8iVxuICAgICAgY29uc3QgZGIgPSBuZXcgRGF0YWJhc2UoZGJQYXRoKTtcbiAgICAgIGxldCBsb2NhbFRvb2xDb3VudCA9IDA7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBsb2NhbFNlcnZlciA9IGRiLnByZXBhcmUoJ1NFTEVDVCBpZCBGUk9NIG1jcF9zZXJ2ZXJzIFdIRVJFIG5hbWUgPSA/JykuZ2V0KCdsb2NhbCcpIGFzIHsgaWQ6IG51bWJlciB9IHwgdW5kZWZpbmVkO1xuICAgICAgICBpZiAobG9jYWxTZXJ2ZXIpIHtcbiAgICAgICAgICBjb25zdCB0b29sQ291bnRSZXN1bHQgPSBkYi5wcmVwYXJlKCdTRUxFQ1QgQ09VTlQoKikgYXMgY291bnQgRlJPTSBtY3BfdG9vbHMgV0hFUkUgc2VydmVyX2lkID0gPyBBTkQgaXNfYXZhaWxhYmxlID0gMSBBTkQgZW5hYmxlZCA9IDEnKS5nZXQobG9jYWxTZXJ2ZXIuaWQpIGFzIHsgY291bnQ6IG51bWJlciB9IHwgdW5kZWZpbmVkO1xuICAgICAgICAgIGxvY2FsVG9vbENvdW50ID0gdG9vbENvdW50UmVzdWx0Py5jb3VudCB8fCAwO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIOWmguaenOaVsOaNruW6k+S4reayoeacieacrOWcsOacjeWKoeWZqOiusOW9le+8jOebtOaOpeS7juWuouaIt+err+iOt+WPllxuICAgICAgICAgIGNvbnN0IGxvY2FsVG9vbHMgPSBpc0Nvbm5lY3RlZCA/IG1jcFNlcnZlckNsaWVudC5nZXRBdmFpbGFibGVUb29scygpIDogW107XG4gICAgICAgICAgbG9jYWxUb29sQ291bnQgPSBsb2NhbFRvb2xzLmxlbmd0aDtcbiAgICAgICAgfVxuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgZGIuY2xvc2UoKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgc2VydmVycy5wdXNoKHtcbiAgICAgICAgbmFtZTogJ2xvY2FsJyxcbiAgICAgICAgZGlzcGxheU5hbWU6ICfmnKzlnLDmnI3liqHlmagnLFxuICAgICAgICB0eXBlOiAnc3RkaW8nLFxuICAgICAgICBzdGF0dXM6IGlzQ29ubmVjdGVkID8gJ2Nvbm5lY3RlZCcgOiAnZGlzY29ubmVjdGVkJyxcbiAgICAgICAgdG9vbENvdW50OiBsb2NhbFRvb2xDb3VudCxcbiAgICAgICAgZGVzY3JpcHRpb246ICfmnKzlnLBNQ1DmnI3liqHlmajvvIzmj5Dkvpvln7rnoYDlt6Xlhbflip/og70nXG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5pys5Zyw5pyN5Yqh5Zmo5L+h5oGv5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIHNlcnZlcnMucHVzaCh7XG4gICAgICAgIG5hbWU6ICdsb2NhbCcsXG4gICAgICAgIGRpc3BsYXlOYW1lOiAn5pys5Zyw5pyN5Yqh5ZmoJyxcbiAgICAgICAgdHlwZTogJ3N0ZGlvJyxcbiAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICB0b29sQ291bnQ6IDAsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAn5pys5ZywTUNQ5pyN5Yqh5Zmo77yM5o+Q5L6b5Z+656GA5bel5YW35Yqf6IO9J1xuICAgICAgfSk7XG4gICAgfVxuICAgIFxuICAgIC8vIOS7juaVsOaNruW6k+iOt+WPluWklumDqOacjeWKoeWZqOS/oeaBr1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBkYlNlcnZlcnMgPSBnZXRTZXJ2ZXJzRnJvbURhdGFiYXNlKCk7XG4gICAgICBcbiAgICAgIC8vIOa3u+WKoOaVsOaNruW6k+S4reeahOacjeWKoeWZqOS/oeaBr++8iOaOkumZpOacrOWcsOacjeWKoeWZqO+8jOWboOS4uuW3sue7j+aJi+WKqOa3u+WKoOS6hu+8iVxuICAgICAgZGJTZXJ2ZXJzLmZvckVhY2goKHNlcnZlcjogRGJTZXJ2ZXJSZXN1bHQpID0+IHtcbiAgICAgICAgLy8g6Lez6L+H5pys5Zyw5pyN5Yqh5Zmo77yM6YG/5YWN6YeN5aSNXG4gICAgICAgIGlmIChzZXJ2ZXIubmFtZSA9PT0gJ2xvY2FsJykge1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgc2VydmVycy5wdXNoKHtcbiAgICAgICAgICBpZDogc2VydmVyLmlkLFxuICAgICAgICAgIG5hbWU6IHNlcnZlci5uYW1lLFxuICAgICAgICAgIGRpc3BsYXlOYW1lOiBzZXJ2ZXIuZGlzcGxheV9uYW1lIHx8IHNlcnZlci5uYW1lLFxuICAgICAgICAgIHR5cGU6IHNlcnZlci50eXBlLFxuICAgICAgICAgIHN0YXR1czogc2VydmVyLnN0YXR1cyB8fCAnZGlzY29ubmVjdGVkJywgLy8g5L2/55So5pWw5o2u5bqT5Lit5a2Y5YKo55qE54q25oCBXG4gICAgICAgICAgdG9vbENvdW50OiBzZXJ2ZXIudG9vbF9jb3VudCB8fCAwLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBzZXJ2ZXIuZGVzY3JpcHRpb24gfHwgYOWklumDqE1DUOacjeWKoeWZqDogJHtzZXJ2ZXIubmFtZX1gLFxuICAgICAgICAgIGxhc3RDb25uZWN0ZWRBdDogc2VydmVyLmxhc3RfY29ubmVjdGVkX2F0LFxuICAgICAgICAgIGVycm9yTWVzc2FnZTogc2VydmVyLmVycm9yX21lc3NhZ2VcbiAgICAgICAgfSk7XG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5aSW6YOo5pyN5Yqh5Zmo5L+h5oGv5aSx6LSlOicsIGVycm9yKTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBzZXJ2ZXJzOiBzZXJ2ZXJzXG4gICAgfSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign6I635Y+W5pyN5Yqh5Zmo5YiX6KGo5aSx6LSlOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAn6I635Y+W5pyN5Yqh5Zmo5YiX6KGo5aSx6LSlJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufSJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJEYXRhYmFzZSIsInBhdGgiLCJkYlBhdGgiLCJqb2luIiwicHJvY2VzcyIsImN3ZCIsImdldFNlcnZlcnNGcm9tRGF0YWJhc2UiLCJkYiIsInNlcnZlcnMiLCJwcmVwYXJlIiwiYWxsIiwiY2xvc2UiLCJHRVQiLCJtY3BTZXJ2ZXJDbGllbnQiLCJyZXF1aXJlIiwiaXNDb25uZWN0ZWQiLCJpc0NsaWVudENvbm5lY3RlZCIsImxvY2FsVG9vbENvdW50IiwibG9jYWxTZXJ2ZXIiLCJnZXQiLCJ0b29sQ291bnRSZXN1bHQiLCJpZCIsImNvdW50IiwibG9jYWxUb29scyIsImdldEF2YWlsYWJsZVRvb2xzIiwibGVuZ3RoIiwicHVzaCIsIm5hbWUiLCJkaXNwbGF5TmFtZSIsInR5cGUiLCJzdGF0dXMiLCJ0b29sQ291bnQiLCJkZXNjcmlwdGlvbiIsImVycm9yIiwiY29uc29sZSIsImRiU2VydmVycyIsImZvckVhY2giLCJzZXJ2ZXIiLCJkaXNwbGF5X25hbWUiLCJ0b29sX2NvdW50IiwibGFzdENvbm5lY3RlZEF0IiwibGFzdF9jb25uZWN0ZWRfYXQiLCJlcnJvck1lc3NhZ2UiLCJlcnJvcl9tZXNzYWdlIiwianNvbiIsInN1Y2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/mcp/server-list/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mcp/mcp-client-server.ts":
/*!******************************************!*\
  !*** ./src/lib/mcp/mcp-client-server.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   McpServerClient: () => (/* binding */ McpServerClient),\n/* harmony export */   mcpServerClient: () => (/* binding */ mcpServerClient)\n/* harmony export */ });\n/* harmony import */ var _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/index.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js\");\n/* harmony import */ var _modelcontextprotocol_sdk_client_stdio_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/stdio.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/stdio.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/**\n * 服务器端MCP客户端实现\n * 只能在Node.js环境中使用，用于API路由\n */ \n\n\n/**\n * 服务器端MCP客户端类\n * 负责与MCP服务器的通信\n */ class McpServerClient {\n    /**\n   * 连接到MCP服务器\n   */ async connect() {\n        try {\n            // 创建stdio传输，直接启动MCP服务器\n            const serverPath = path__WEBPACK_IMPORTED_MODULE_2__.join(process.cwd(), 'src', 'lib', 'mcp', 'mcp-server.ts');\n            this.transport = new _modelcontextprotocol_sdk_client_stdio_js__WEBPACK_IMPORTED_MODULE_1__.StdioClientTransport({\n                command: 'npx',\n                args: [\n                    'tsx',\n                    serverPath\n                ]\n            });\n            // 创建客户端\n            this.client = new _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__.Client({\n                name: 'kun-agent-server-client',\n                version: '1.0.0'\n            }, {\n                capabilities: {\n                    tools: {}\n                }\n            });\n            // 连接到服务器\n            await this.client.connect(this.transport);\n            this.isConnected = true;\n            // 获取可用工具\n            await this.refreshTools();\n            console.log('服务器端MCP客户端连接成功');\n            return true;\n        } catch (error) {\n            console.error('服务器端MCP客户端连接失败:', error);\n            await this.disconnect();\n            return false;\n        }\n    }\n    /**\n   * 断开连接\n   */ async disconnect() {\n        try {\n            if (this.client) {\n                await this.client.close();\n                this.client = null;\n            }\n            if (this.transport) {\n                await this.transport.close();\n                this.transport = null;\n            }\n            this.isConnected = false;\n            this.availableTools = [];\n            console.log('服务器端MCP客户端已断开连接');\n        } catch (error) {\n            console.error('断开MCP连接时出错:', error);\n        }\n    }\n    /**\n   * 检查是否已连接\n   */ isClientConnected() {\n        return this.isConnected && this.client !== null;\n    }\n    /**\n   * 刷新工具列表\n   */ async refreshTools() {\n        if (!this.client || !this.isConnected) {\n            return [];\n        }\n        try {\n            const response = await this.client.listTools();\n            this.availableTools = response.tools.map((tool)=>{\n                // 清理inputSchema，移除$schema字段以符合Ollama要求\n                const cleanInputSchema = tool.inputSchema ? {\n                    ...tool.inputSchema\n                } : {};\n                if (cleanInputSchema && typeof cleanInputSchema === 'object' && '$schema' in cleanInputSchema) {\n                    delete cleanInputSchema.$schema;\n                }\n                return {\n                    name: tool.name,\n                    description: tool.description || '',\n                    inputSchema: cleanInputSchema\n                };\n            });\n            console.log(`已获取 ${this.availableTools.length} 个MCP工具`);\n            return this.availableTools;\n        } catch (error) {\n            console.error('获取MCP工具列表失败:', error);\n            return [];\n        }\n    }\n    /**\n   * 获取可用工具列表\n   */ getAvailableTools() {\n        return this.availableTools;\n    }\n    /**\n   * 检查工具是否可用\n   */ isToolAvailable(toolName) {\n        return this.availableTools.some((tool)=>tool.name === toolName);\n    }\n    /**\n   * 调用工具\n   */ async callTool(toolCall) {\n        if (!this.client || !this.isConnected) {\n            return {\n                content: [\n                    {\n                        type: 'text',\n                        text: 'MCP客户端未连接'\n                    }\n                ],\n                isError: true\n            };\n        }\n        try {\n            const result = await this.client.callTool({\n                name: toolCall.name,\n                arguments: toolCall.arguments\n            });\n            // 根据最新的MCP SDK，result.content 已经是正确的格式\n            const content = result.content || [\n                {\n                    type: 'text',\n                    text: '工具执行成功，但没有返回内容'\n                }\n            ];\n            return {\n                content: content.map((item)=>({\n                        type: 'text',\n                        text: item.type === 'text' ? item.text : JSON.stringify(item)\n                    })),\n                isError: false\n            };\n        } catch (error) {\n            console.error('工具调用失败:', error);\n            return {\n                content: [\n                    {\n                        type: 'text',\n                        text: `工具调用失败: ${error instanceof Error ? error.message : '未知错误'}`\n                    }\n                ],\n                isError: true\n            };\n        }\n    }\n    constructor(){\n        this.client = null;\n        this.transport = null;\n        this.isConnected = false;\n        this.availableTools = [];\n    }\n}\n// 导出类和单例实例\n\nconst mcpServerClient = new McpServerClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mcp/mcp-client-server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/ajv","vendor-chunks/@modelcontextprotocol","vendor-chunks/uri-js","vendor-chunks/cross-spawn","vendor-chunks/which","vendor-chunks/isexe","vendor-chunks/json-schema-traverse","vendor-chunks/fast-json-stable-stringify","vendor-chunks/fast-deep-equal","vendor-chunks/path-key","vendor-chunks/shebang-command","vendor-chunks/shebang-regex"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Fserver-list%2Froute&page=%2Fapi%2Fmcp%2Fserver-list%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Fserver-list%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();