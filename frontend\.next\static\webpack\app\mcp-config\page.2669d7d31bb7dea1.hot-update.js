"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mcp-config/page",{

/***/ "(app-pages-browser)/./src/app/Sidebar.tsx":
/*!*****************************!*\
  !*** ./src/app/Sidebar.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-right.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/workflow.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,History,Home,PanelLeft,PanelRight,Plus,Server,Settings,Workflow!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _theme_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/theme/components/ThemeToggle */ \"(app-pages-browser)/./src/theme/components/ThemeToggle.tsx\");\n/* harmony import */ var _theme_components_ColorThemeSwitcher__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/theme/components/ColorThemeSwitcher */ \"(app-pages-browser)/./src/theme/components/ColorThemeSwitcher.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_ConversationContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/ConversationContext */ \"(app-pages-browser)/./src/contexts/ConversationContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// 简化的侧边栏状态管理\nfunction useSidebarState() {\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const toggleSidebar = ()=>{\n        const newState = !isExpanded;\n        setIsExpanded(newState);\n        if (true) {\n            document.documentElement.setAttribute('data-sidebar-state', newState ? 'expanded' : 'collapsed');\n            localStorage.setItem('sidebar-expanded', JSON.stringify(newState));\n        }\n    };\n    // 在客户端挂载后同步真实状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useSidebarState.useEffect\": ()=>{\n            if (true) {\n                const state = document.documentElement.getAttribute('data-sidebar-state');\n                const actualState = state === 'expanded';\n                if (actualState !== isExpanded) {\n                    setIsExpanded(actualState);\n                }\n            }\n        }\n    }[\"useSidebarState.useEffect\"], [\n        isExpanded\n    ]);\n    return {\n        isExpanded,\n        toggleSidebar\n    };\n}\n_s(useSidebarState, \"VGKo2U2z4tCZFJeh2IZSoSqn0qE=\");\nfunction Sidebar(param) {\n    let { onCreateConversation, onLoadConversation, onDeleteConversation } = param;\n    _s1();\n    const { isExpanded, toggleSidebar } = useSidebarState();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    // 使用全局对话状态\n    const { conversations, currentConversation } = (0,_contexts_ConversationContext__WEBPACK_IMPORTED_MODULE_6__.useConversations)();\n    // 处理创建新对话的逻辑\n    const handleNewConversation = ()=>{\n        if (pathname === '/simple-chat') {\n            onCreateConversation();\n        } else {\n            router.push('/simple-chat?new=true');\n        }\n    };\n    // 处理进入聊天页面的逻辑\n    const handleGoToChat = ()=>{\n        const hasConversations = conversations && conversations.length > 0;\n        if (pathname === '/simple-chat') {\n            if (hasConversations && !currentConversation) {\n                const latestConversation = conversations.sort((a, b)=>new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())[0];\n                onLoadConversation(latestConversation.id);\n            } else if (!hasConversations) {\n                onCreateConversation();\n            }\n        } else {\n            if (hasConversations) {\n                const latestConversation = conversations.sort((a, b)=>new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())[0];\n                router.push(\"/simple-chat?id=\".concat(latestConversation.id));\n            } else {\n                router.push('/simple-chat?new=true');\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sidebar-container relative bg-theme-card border-r border-theme-border flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"group p-4 border-b border-theme-border flex items-center justify-center relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/\",\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"/assets/<EMAIL>\",\n                                alt: \"Kun Agent Logo\",\n                                className: \"w-8 h-8 transition-all duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"sidebar-text text-lg font-bold text-theme-foreground ml-2\",\n                                children: \"Kun Agent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleSidebar,\n                        className: \"p-1.5 rounded-full text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-card-hover transition-all duration-300 opacity-0 group-hover:opacity-100 absolute right-0 translate-x-1/2 top-1/2 -translate-y-1/2 bg-theme-card border border-theme-border shadow-sm\",\n                        children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleNewConversation,\n                    className: \"sidebar-button group relative w-full flex items-center gap-3 p-3 rounded-lg bg-theme-primary text-white hover:bg-theme-primary-hover transition-colors duration-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-5 h-5 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sidebar-text text-sm font-semibold\",\n                            children: \"新建对话\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                            children: \"新建对话\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pl-2 flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/\",\n                            className: \"sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 \".concat(pathname === '/' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"首页\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"首页\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/mcp-config\",\n                            className: \"sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 \".concat(pathname === '/mcp-config' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"MCP配置\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"MCP配置\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/model-manager\",\n                            className: \"sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 \".concat(pathname === '/model-manager' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"模型管理\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"模型管理\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/agents\",\n                            className: \"sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 \".concat(pathname === '/agents' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"智能体管理\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"智能体管理\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleGoToChat,\n                            className: \"sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 w-full text-left \".concat(pathname === '/simple-chat' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-5 h-5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm\",\n                                    children: \"AI聊天\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: conversations.length > 0 ? \"进入最新对话\" : \"开始AI聊天\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-theme-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/conversations\",\n                                className: \"sidebar-button sidebar-nav-item group relative w-full flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 \".concat(pathname === '/conversations' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-card-hover'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sidebar-icon-container\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-5 h-5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sidebar-text text-sm\",\n                                        children: \"对话历史\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                        children: \"对话历史\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/settings\",\n                                className: \"sidebar-button sidebar-nav-item group relative w-full flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 \".concat(pathname === '/settings' ? 'active text-theme-foreground' : 'text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-card-hover'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sidebar-icon-container\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Brain_History_Home_PanelLeft_PanelRight_Plus_Server_Settings_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-5 h-5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sidebar-text text-sm\",\n                                        children: \"设置\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                        children: \"设置\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 pb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sidebar-button group relative w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 hover:bg-theme-card-hover\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-icon-container theme-toggle-icon\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__.ThemeToggle, {\n                                            variant: \"icon\",\n                                            size: \"sm\",\n                                            className: \"sidebar-collapsed-only\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this),\n                                        isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_components_ColorThemeSwitcher__WEBPACK_IMPORTED_MODULE_3__.ColorThemeSwitcher, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 30\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-text text-sm text-theme-foreground-muted flex-1\",\n                                    children: \"外观主题\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-text\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__.ThemeToggle, {\n                                        variant: \"icon\",\n                                        size: \"sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none\",\n                                    children: \"切换主题\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\Sidebar.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n_s1(Sidebar, \"3ShVQCsCist8r0toTg4HJbtlGSQ=\", false, function() {\n    return [\n        useSidebarState,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        _contexts_ConversationContext__WEBPACK_IMPORTED_MODULE_6__.useConversations\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/Sidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/ConversationContext.tsx":
/*!**********************************************!*\
  !*** ./src/contexts/ConversationContext.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationProvider: () => (/* binding */ ConversationProvider),\n/* harmony export */   useConversations: () => (/* binding */ useConversations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ConversationProvider,useConversations auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ConversationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// 全局缓存配置\nconst CACHE_DURATION = 30000; // 30秒缓存\nlet globalConversationsCache = [];\nlet globalCacheTimestamp = 0;\nlet isLoadingGlobal = false;\nfunction ConversationProvider(param) {\n    let { children } = param;\n    _s();\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 防止重复请求的引用\n    const loadingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 检查缓存是否有效\n    const isCacheValid = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[isCacheValid]\": ()=>{\n            const now = Date.now();\n            return globalConversationsCache.length > 0 && now - globalCacheTimestamp < CACHE_DURATION;\n        }\n    }[\"ConversationProvider.useCallback[isCacheValid]\"], []);\n    // 加载对话列表\n    const loadConversations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[loadConversations]\": async function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n            // 如果正在加载中，直接返回\n            if (loadingRef.current || isLoadingGlobal) {\n                return;\n            }\n            // 如果不强制刷新且缓存有效，使用缓存\n            if (!forceRefresh && isCacheValid()) {\n                setConversations(globalConversationsCache);\n                return;\n            }\n            try {\n                loadingRef.current = true;\n                isLoadingGlobal = true;\n                setLoading(true);\n                setError(null);\n                // 取消之前的请求\n                if (abortControllerRef.current) {\n                    abortControllerRef.current.abort();\n                }\n                // 创建新的AbortController\n                abortControllerRef.current = new AbortController();\n                console.log('[ConversationProvider] 开始加载对话列表...');\n                const response = await fetch('/api/conversations', {\n                    signal: abortControllerRef.current.signal\n                });\n                const data = await response.json();\n                if (data.success) {\n                    const conversationList = data.conversations || [];\n                    // 更新全局缓存\n                    globalConversationsCache = conversationList;\n                    globalCacheTimestamp = Date.now();\n                    setConversations(conversationList);\n                    console.log(\"[ConversationProvider] 成功加载 \".concat(conversationList.length, \" 个对话\"));\n                } else {\n                    setError(data.error || '加载对话列表失败');\n                }\n            } catch (err) {\n                if (err instanceof Error && err.name === 'AbortError') {\n                    console.log('[ConversationProvider] 请求被取消');\n                    return;\n                }\n                setError('网络错误，加载对话列表失败');\n                console.error('加载对话列表失败:', err);\n            } finally{\n                loadingRef.current = false;\n                isLoadingGlobal = false;\n                setLoading(false);\n                abortControllerRef.current = null;\n            }\n        }\n    }[\"ConversationProvider.useCallback[loadConversations]\"], [\n        isCacheValid\n    ]);\n    // 创建新对话\n    const createConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[createConversation]\": async (title, model)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始创建对话: \".concat(title, \", 模型: \").concat(model));\n                const response = await fetch('/api/conversations', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        title,\n                        model\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 对话创建成功:\", data.conversation);\n                    // 刷新对话列表\n                    await loadConversations(true);\n                    // 切换到新创建的对话\n                    setCurrentConversation(data.conversation);\n                    return data.conversation.id;\n                } else {\n                    console.error(\"[ConversationProvider] 创建对话失败:\", data.error);\n                    setError(data.error || '创建对话失败');\n                    return null;\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 创建对话异常:\", err);\n                setError('网络错误，创建对话失败');\n                return null;\n            }\n        }\n    }[\"ConversationProvider.useCallback[createConversation]\"], [\n        loadConversations\n    ]);\n    // 切换对话\n    const switchConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[switchConversation]\": async (id)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始切换到对话 \".concat(id));\n                const response = await fetch(\"/api/conversations/\".concat(id));\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 成功获取对话 \".concat(id, \" 信息:\"), data.conversation);\n                    setCurrentConversation(data.conversation);\n                } else {\n                    console.error(\"[ConversationProvider] 切换对话 \".concat(id, \" 失败:\"), data.error);\n                    setError(data.error || '切换对话失败');\n                    throw new Error(data.error || '切换对话失败');\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 切换对话 \".concat(id, \" 异常:\"), err);\n                setError('网络错误，切换对话失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[switchConversation]\"], []);\n    // 删除对话\n    const deleteConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[deleteConversation]\": async (id)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始删除对话 \".concat(id));\n                const response = await fetch(\"/api/conversations/\".concat(id), {\n                    method: 'DELETE'\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 对话 \".concat(id, \" 删除成功\"));\n                    // 从本地状态中移除\n                    setConversations({\n                        \"ConversationProvider.useCallback[deleteConversation]\": (prev)=>prev.filter({\n                                \"ConversationProvider.useCallback[deleteConversation]\": (conv)=>conv.id !== id\n                            }[\"ConversationProvider.useCallback[deleteConversation]\"])\n                    }[\"ConversationProvider.useCallback[deleteConversation]\"]);\n                    // 更新全局缓存\n                    globalConversationsCache = globalConversationsCache.filter({\n                        \"ConversationProvider.useCallback[deleteConversation]\": (conv)=>conv.id !== id\n                    }[\"ConversationProvider.useCallback[deleteConversation]\"]);\n                    // 如果删除的是当前对话，清空当前对话\n                    if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === id) {\n                        setCurrentConversation(null);\n                    }\n                } else {\n                    console.error(\"[ConversationProvider] 删除对话 \".concat(id, \" 失败:\"), data.error);\n                    setError(data.error || '删除对话失败');\n                    throw new Error(data.error || '删除对话失败');\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 删除对话 \".concat(id, \" 异常:\"), err);\n                setError('网络错误，删除对话失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[deleteConversation]\"], [\n        currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id\n    ]);\n    // 更新对话标题\n    const updateConversationTitle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[updateConversationTitle]\": async (id, title)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始更新对话 \".concat(id, \" 标题: \").concat(title));\n                const response = await fetch(\"/api/conversations/\".concat(id), {\n                    method: 'PATCH',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        title\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 对话 \".concat(id, \" 标题更新成功\"));\n                    // 更新本地状态\n                    setConversations({\n                        \"ConversationProvider.useCallback[updateConversationTitle]\": (prev)=>prev.map({\n                                \"ConversationProvider.useCallback[updateConversationTitle]\": (conv)=>conv.id === id ? {\n                                        ...conv,\n                                        title\n                                    } : conv\n                            }[\"ConversationProvider.useCallback[updateConversationTitle]\"])\n                    }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    // 更新全局缓存\n                    globalConversationsCache = globalConversationsCache.map({\n                        \"ConversationProvider.useCallback[updateConversationTitle]\": (conv)=>conv.id === id ? {\n                                ...conv,\n                                title\n                            } : conv\n                    }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    // 如果是当前对话，也更新当前对话状态\n                    if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === id) {\n                        setCurrentConversation({\n                            \"ConversationProvider.useCallback[updateConversationTitle]\": (prev)=>prev ? {\n                                    ...prev,\n                                    title\n                                } : null\n                        }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    }\n                } else {\n                    console.error(\"[ConversationProvider] 更新对话 \".concat(id, \" 标题失败:\"), data.error);\n                    setError(data.error || '更新对话标题失败');\n                    throw new Error(data.error || '更新对话标题失败');\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 更新对话 \".concat(id, \" 标题异常:\"), err);\n                setError('网络错误，更新对话标题失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[updateConversationTitle]\"], [\n        currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id\n    ]);\n    // 初始化时加载对话列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationProvider.useEffect\": ()=>{\n            loadConversations();\n        }\n    }[\"ConversationProvider.useEffect\"], [\n        loadConversations\n    ]);\n    // 组件卸载时清理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationProvider.useEffect\": ()=>{\n            return ({\n                \"ConversationProvider.useEffect\": ()=>{\n                    if (abortControllerRef.current) {\n                        abortControllerRef.current.abort();\n                    }\n                }\n            })[\"ConversationProvider.useEffect\"];\n        }\n    }[\"ConversationProvider.useEffect\"], []);\n    const value = {\n        conversations,\n        currentConversation,\n        loading,\n        error,\n        loadConversations,\n        createConversation,\n        switchConversation,\n        deleteConversation,\n        updateConversationTitle,\n        setCurrentConversation\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConversationContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\contexts\\\\ConversationContext.tsx\",\n        lineNumber: 286,\n        columnNumber: 5\n    }, this);\n}\n_s(ConversationProvider, \"eDx1BKB9zBJkVZ1FPc7VJppvBuo=\");\n_c = ConversationProvider;\nfunction useConversations() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ConversationContext);\n    if (context === undefined) {\n        throw new Error('useConversations must be used within a ConversationProvider');\n    }\n    return context;\n}\n_s1(useConversations, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ConversationProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ConversationContext.tsx\n"));

/***/ })

});