'use client';

import React, { useState, useEffect } from 'react';
import {
  Plus,
  Settings,
  PanelLeft,
  PanelRight,
  Home,
  Server,
  Brain,
  Bot,
  Workflow,
  History
} from 'lucide-react';
import { Conversation } from '@/lib/database';
import { ThemeToggle } from '@/theme/components/ThemeToggle';
import { ColorThemeSwitcher } from '@/theme/components/ColorThemeSwitcher';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useConversations } from '@/contexts/ConversationContext';

interface SidebarProps {
  // 移除props，改为使用全局状态
  onCreateConversation?: () => void;
  onLoadConversation?: (conversationId: number) => void;
  onDeleteConversation?: (conversationId: number) => void;
}

// 简化的侧边栏状态管理
function useSidebarState() {
  const [isExpanded, setIsExpanded] = useState(true);

  const toggleSidebar = () => {
    const newState = !isExpanded;
    setIsExpanded(newState);
    
    if (typeof window !== 'undefined') {
      document.documentElement.setAttribute('data-sidebar-state', newState ? 'expanded' : 'collapsed');
      localStorage.setItem('sidebar-expanded', JSON.stringify(newState));
    }
  };

  // 在客户端挂载后同步真实状态
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const state = document.documentElement.getAttribute('data-sidebar-state');
      const actualState = state === 'expanded';
      if (actualState !== isExpanded) {
        setIsExpanded(actualState);
      }
    }
  }, [isExpanded]);

  return { isExpanded, toggleSidebar };
}

export function Sidebar({
  onCreateConversation,
  onLoadConversation,
  onDeleteConversation,
}: SidebarProps) {
  const { isExpanded, toggleSidebar } = useSidebarState();
  const pathname = usePathname();
  const router = useRouter();

  // 使用全局对话状态
  const { conversations, currentConversation } = useConversations();

  // 处理创建新对话的逻辑
  const handleNewConversation = () => {
    if (pathname === '/simple-chat') {
      onCreateConversation();
    } else {
      router.push('/simple-chat?new=true');
    }
  };

  // 处理进入聊天页面的逻辑
  const handleGoToChat = () => {
    const hasConversations = conversations && conversations.length > 0;
    
    if (pathname === '/simple-chat') {
      if (hasConversations && !currentConversation) {
        const latestConversation = conversations.sort((a, b) => 
          new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
        )[0];
        onLoadConversation(latestConversation.id);
      } else if (!hasConversations) {
        onCreateConversation();
      }
    } else {
      if (hasConversations) {
        const latestConversation = conversations.sort((a, b) => 
          new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
        )[0];
        router.push(`/simple-chat?id=${latestConversation.id}`);
      } else {
        router.push('/simple-chat?new=true');
      }
    }
  };

  return (
    <div className="sidebar-container relative bg-theme-card border-r border-theme-border flex flex-col">
      {/* 顶部区域 */}
      <div className="group p-4 border-b border-theme-border flex items-center justify-center relative">
        <Link href="/" className="flex items-center gap-2">
          <img
            src="/assets/<EMAIL>"
            alt="Kun Agent Logo"
            className="w-8 h-8 transition-all duration-300"
          />
          <h1 className="sidebar-text text-lg font-bold text-theme-foreground ml-2">
            Kun Agent
          </h1>
        </Link>
        <button
          onClick={toggleSidebar}
          className="p-1.5 rounded-full text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-card-hover transition-all duration-300 opacity-0 group-hover:opacity-100 absolute right-0 translate-x-1/2 top-1/2 -translate-y-1/2 bg-theme-card border border-theme-border shadow-sm"
        >
          {isExpanded ? (
            <PanelLeft className="w-4 h-4" />
          ) : (
            <PanelRight className="w-4 h-4" />
          )}
        </button>
      </div>

      {/* 新建对话区域 */}
      <div className="p-3">
        <button
          onClick={handleNewConversation}
          className="sidebar-button group relative w-full flex items-center gap-3 p-3 rounded-lg bg-theme-primary text-white hover:bg-theme-primary-hover transition-colors duration-200"
        >
          <Plus className="w-5 h-5 flex-shrink-0" />
          <span className="sidebar-text text-sm font-semibold">新建对话</span>
          <span className="sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none">
            新建对话
          </span>
        </button>
      </div>
      
      {/* 导航菜单区域 */}
      <div className="pl-2 flex-1">
        <nav className="space-y-2">
          <Link
            href="/"
            className={`sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 ${
              pathname === '/' 
                ? 'active text-theme-foreground' 
                : 'text-theme-foreground-muted hover:text-theme-foreground'
            }`}
          >
            <div className="sidebar-icon-container">
              <Home className="w-5 h-5 flex-shrink-0" />
            </div>
            <span className="sidebar-text text-sm">首页</span>
            <span className="sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none">
              首页
            </span>
          </Link>
          
          <Link
            href="/mcp-config"
            className={`sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 ${
              pathname === '/mcp-config' 
                ? 'active text-theme-foreground' 
                : 'text-theme-foreground-muted hover:text-theme-foreground'
            }`}
          >
            <div className="sidebar-icon-container">
              <Server className="w-5 h-5 flex-shrink-0" />
            </div>
            <span className="sidebar-text text-sm">MCP配置</span>
            <span className="sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none">
              MCP配置
            </span>
          </Link>
          
          <Link
            href="/model-manager"
            className={`sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 ${
              pathname === '/model-manager' 
                ? 'active text-theme-foreground' 
                : 'text-theme-foreground-muted hover:text-theme-foreground'
            }`}
          >
            <div className="sidebar-icon-container">
              <Brain className="w-5 h-5 flex-shrink-0" />
            </div>
            <span className="sidebar-text text-sm">模型管理</span>
            <span className="sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none">
              模型管理
            </span>
          </Link>
          
          <Link
            href="/agents"
            className={`sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 ${
              pathname === '/agents' 
                ? 'active text-theme-foreground' 
                : 'text-theme-foreground-muted hover:text-theme-foreground'
            }`}
          >
            <div className="sidebar-icon-container">
              <Workflow className="w-5 h-5 flex-shrink-0" />
            </div>
            <span className="sidebar-text text-sm">智能体管理</span>
            <span className="sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none">
              智能体管理
            </span>
          </Link>
          
          <button
            onClick={handleGoToChat}
            className={`sidebar-nav-item sidebar-button group relative flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 w-full text-left ${
              pathname === '/simple-chat' 
                ? 'active text-theme-foreground' 
                : 'text-theme-foreground-muted hover:text-theme-foreground'
            }`}
          >
            <div className="sidebar-icon-container">
              <Bot className="w-5 h-5 flex-shrink-0" />
            </div>
            <span className="sidebar-text text-sm">AI聊天</span>
            <span className="sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none">
              {conversations.length > 0 ? "进入最新对话" : "开始AI聊天"}
            </span>
          </button>
        </nav>
      </div>

      {/* 底部区域 */}
      <div className="border-t border-theme-border">
        <div className="p-3 space-y-2">
          <Link
            href="/conversations"
            className={`sidebar-button sidebar-nav-item group relative w-full flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 ${
              pathname === '/conversations'
                ? 'active text-theme-foreground'
                : 'text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-card-hover'
            }`}
          >
            <div className="sidebar-icon-container">
              <History className="w-5 h-5 flex-shrink-0" />
            </div>
            <span className="sidebar-text text-sm">对话历史</span>
            <span className="sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none">
              对话历史
            </span>
          </Link>
          
          {/* 设置按钮 */}
          <Link
            href="/settings"
            className={`sidebar-button sidebar-nav-item group relative w-full flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 ${
              pathname === '/settings'
                ? 'active text-theme-foreground'
                : 'text-theme-foreground-muted hover:text-theme-foreground hover:bg-theme-card-hover'
            }`}
          >
            <div className="sidebar-icon-container">
              <Settings className="w-5 h-5 flex-shrink-0" />
            </div>
            <span className="sidebar-text text-sm">设置</span>
            <span className="sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none">
              设置
            </span>
          </Link>
        </div>
        
        {/* 主题切换区域 */}
        <div className="px-3 pb-3">
          <div className="sidebar-button group relative w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 hover:bg-theme-card-hover">
            <div className="sidebar-icon-container theme-toggle-icon">
              <ThemeToggle variant="icon" size="sm" className="sidebar-collapsed-only" />
              {isExpanded && <ColorThemeSwitcher />}
            </div>
            <span className="sidebar-text text-sm text-theme-foreground-muted flex-1">外观主题</span>
            <div className="sidebar-text">
              <ThemeToggle variant="icon" size="sm" />
            </div>
            <span className="sidebar-tooltip absolute left-full ml-4 px-2 py-1 rounded-md text-sm bg-theme-card-hover text-theme-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10 pointer-events-none">
              切换主题
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}