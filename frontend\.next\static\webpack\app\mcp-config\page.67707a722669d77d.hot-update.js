"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mcp-config/page",{

/***/ "(app-pages-browser)/./src/app/mcp-config/page.tsx":
/*!*************************************!*\
  !*** ./src/app/mcp-config/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ McpConfigPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _hooks_useMcpConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hooks/useMcpConfig */ \"(app-pages-browser)/./src/app/mcp-config/hooks/useMcpConfig.ts\");\n/* harmony import */ var _components_AddServerModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/AddServerModal */ \"(app-pages-browser)/./src/app/mcp-config/components/AddServerModal.tsx\");\n/* harmony import */ var _components_ToolsModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ToolsModal */ \"(app-pages-browser)/./src/app/mcp-config/components/ToolsModal.tsx\");\n/* harmony import */ var _barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Axe,Plus,RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Axe,Plus,RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Axe,Plus,RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Axe,Plus,RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/axe.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_notification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/notification */ \"(app-pages-browser)/./src/components/notification/index.ts\");\n/* harmony import */ var _components_Modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Modal */ \"(app-pages-browser)/./src/components/Modal.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../Sidebar */ \"(app-pages-browser)/./src/app/Sidebar.tsx\");\n/* harmony import */ var _components_Loading__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Loading */ \"(app-pages-browser)/./src/components/Loading.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction McpConfigPage() {\n    _s();\n    const { servers, tools, loading, toolsLoading, showAddModal, showToolsModal, selectedTab, selectedServer, newServer, setShowAddModal, setShowToolsModal, setNewServer, setTools, loadServers, loadTools, handleTabChange, handleServerSelect, checkServerStatus, refreshTools, handleDeleteTool, handleUseTool, handleAddServer, handleDeleteServer, executionResult, setExecutionResult, usingToolId } = (0,_hooks_useMcpConfig__WEBPACK_IMPORTED_MODULE_1__.useMcpConfig)();\n    // 操作类弹窗\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [serverToDelete, setServerToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    var _useNotification;\n    const notification = (_useNotification = _components_notification__WEBPACK_IMPORTED_MODULE_5__.useNotification === null || _components_notification__WEBPACK_IMPORTED_MODULE_5__.useNotification === void 0 ? void 0 : (0,_components_notification__WEBPACK_IMPORTED_MODULE_5__.useNotification)()) !== null && _useNotification !== void 0 ? _useNotification : null;\n    // 移除本地对话状态，改为使用全局状态\n    // 侧边栏事件处理\n    const handleCreateConversation = ()=>{\n        window.location.href = '/simple-chat?new=true';\n    };\n    const handleLoadConversation = (conversationId)=>{\n        window.location.href = \"/simple-chat?id=\".concat(conversationId);\n    };\n    const handleDeleteConversation = async (conversationId)=>{\n    // 删除逻辑移到全局状态管理中\n    };\n    // 删除服务器弹窗触发\n    const handleDeleteServerModal = (server)=>{\n        setServerToDelete(server);\n        setDeleteModalOpen(true);\n    };\n    // 确认删除服务器\n    const confirmDeleteServer = async ()=>{\n        if (!serverToDelete) return;\n        try {\n            // 本地服务器不允许删除\n            if (serverToDelete.name === 'local') {\n                var _notification_error;\n                notification && ((_notification_error = notification.error) === null || _notification_error === void 0 ? void 0 : _notification_error.call(notification, '本地服务器不支持删除操作'));\n                setDeleteModalOpen(false);\n                setServerToDelete(null);\n                return;\n            }\n            // 先删除数据库记录\n            const response = await fetch(\"/api/mcp/servers/\".concat(serverToDelete.id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                // 然后删除配置文件中的记录\n                const configResponse = await fetch('/api/mcp/config', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'delete',\n                        serverName: serverToDelete.name\n                    })\n                });\n                if (configResponse.ok) {\n                    var _notification_success;\n                    notification && ((_notification_success = notification.success) === null || _notification_success === void 0 ? void 0 : _notification_success.call(notification, '服务器删除成功', \"服务器「\".concat(serverToDelete.displayName || serverToDelete.name, \"」已删除\")));\n                    if (selectedServer === serverToDelete.name) {\n                        handleServerSelect(null);\n                        setTools([]);\n                    }\n                    await loadServers();\n                } else {\n                    var _notification_error1;\n                    const configErrorData = await configResponse.json();\n                    notification && ((_notification_error1 = notification.error) === null || _notification_error1 === void 0 ? void 0 : _notification_error1.call(notification, '删除配置文件失败', configErrorData.error || '未知错误'));\n                }\n            } else {\n                var _notification_error2;\n                const errorData = await response.json();\n                notification && ((_notification_error2 = notification.error) === null || _notification_error2 === void 0 ? void 0 : _notification_error2.call(notification, '删除服务器失败', errorData.error || '未知错误'));\n            }\n        } catch (error) {\n            var _notification_error3;\n            notification && ((_notification_error3 = notification.error) === null || _notification_error3 === void 0 ? void 0 : _notification_error3.call(notification, '删除服务器失败', error instanceof Error ? error.message : '未知错误'));\n        } finally{\n            setDeleteModalOpen(false);\n            setServerToDelete(null);\n        }\n    };\n    const statusClasses = {\n        connected: 'bg-theme-success',\n        error: 'bg-theme-error',\n        disconnected: 'bg-theme-foreground-muted',\n        connecting: 'bg-yellow-500'\n    };\n    const borderClasses = {\n        connecting: 'ring-2 ring-yellow-500/50 ring-offset-2 ring-offset-theme-background-secondary animate-pulse',\n        selected: 'ring-2 ring-theme-primary ring-offset-2 ring-offset-theme-background-secondary',\n        default: 'border-theme-border hover:border-theme-border-secondary'\n    };\n    const getBorderClass = (server)=>{\n        if (server.status === 'connecting') return borderClasses.connecting;\n        if (selectedServer === server.name) return borderClasses.selected;\n        return borderClasses.default;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notification__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-screen bg-theme-background-secondary\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_7__.Sidebar, {\n                        onCreateConversation: handleCreateConversation,\n                        onLoadConversation: handleLoadConversation,\n                        onDeleteConversation: handleDeleteConversation\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loading__WEBPACK_IMPORTED_MODULE_8__.PageLoading, {\n                            text: \"正在加载MCP服务器配置...\",\n                            fullScreen: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notification__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-theme-background-secondary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_7__.Sidebar, {\n                    conversations: conversations,\n                    currentConversation: currentConversation,\n                    onCreateConversation: handleCreateConversation,\n                    onLoadConversation: handleLoadConversation,\n                    onDeleteConversation: handleDeleteConversation\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-auto scrollbar-thin\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen bg-theme-background-secondary transition-colors duration-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notification__WEBPACK_IMPORTED_MODULE_5__.NotificationManager, {\n                                position: \"top-right\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"max-w-7xl mx-auto py-8 sm:px-6 lg:px-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-6 sm:px-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"page-title\",\n                                                                children: \"\\uD83C\\uDF10MCP 服务器配置\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"page-subtitle mt-2\",\n                                                                children: \"管理模型上下文协议服务器，配置工具和连接\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowAddModal(true),\n                                                            className: \"inline-flex items-center gap-2 px-4 py-2 bg-theme-primary text-white rounded-lg hover:bg-theme-primary-hover transition-colors duration-200 font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"添加服务器\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-b border-theme-border\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                    className: \"-mb-px flex space-x-8\",\n                                                    children: [\n                                                        {\n                                                            key: 'all',\n                                                            label: '全部',\n                                                            count: servers.length\n                                                        },\n                                                        {\n                                                            key: 'local',\n                                                            label: '本地服务器',\n                                                            count: servers.filter((s)=>s.type === 'stdio').length\n                                                        },\n                                                        {\n                                                            key: 'external',\n                                                            label: '外部服务器',\n                                                            count: servers.filter((s)=>s.type !== 'stdio').length\n                                                        }\n                                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleTabChange(tab.key),\n                                                            className: \"\".concat(selectedTab === tab.key ? 'border-theme-primary text-theme-primary' : 'border-transparent text-theme-foreground-muted hover:text-theme-foreground hover:border-theme-border-secondary', \" whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors duration-200 focus:outline-none\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tab.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-theme-background-tertiary text-theme-foreground-secondary py-0.5 px-2.5 rounded-full text-xs font-medium\",\n                                                                    children: tab.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, tab.key, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                    children: servers.filter((server)=>{\n                                                        if (selectedTab === 'all') return true;\n                                                        if (selectedTab === 'local') return server.type === 'stdio';\n                                                        if (selectedTab === 'external') return server.type !== 'stdio';\n                                                        return true;\n                                                    }).map((server)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-theme-card border rounded-lg p-4 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 \".concat(getBorderClass(server)),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start justify-between mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-base font-semibold text-theme-foreground truncate\",\n                                                                                children: server.displayName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                lineNumber: 249,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                            lineNumber: 248,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-1 pl-4\",\n                                                                            children: [\n                                                                                server.type !== 'stdio' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        checkServerStatus(server.name);\n                                                                                    },\n                                                                                    className: \"text-theme-foreground-muted hover:text-theme-primary p-1.5 rounded-full hover:bg-theme-primary/10 transition-colors\",\n                                                                                    title: \"检查连接状态\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"w-3.5 h-3.5 \".concat(server.status === 'connecting' ? 'animate-spin' : '')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                        lineNumber: 261,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                    lineNumber: 253,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                server.name !== 'local' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        handleDeleteServerModal(server);\n                                                                                    },\n                                                                                    className: \"text-theme-foreground-muted hover:text-theme-error p-1.5 rounded-full hover:bg-theme-error/10 transition-colors\",\n                                                                                    title: \"删除服务器\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                        className: \"w-3.5 h-3.5\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                        lineNumber: 273,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                    lineNumber: 265,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                            lineNumber: 251,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-theme-foreground-muted my-3 h-10 overflow-hidden\",\n                                                                    children: server.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                server.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-theme-error mb-2 truncate\",\n                                                                    title: server.errorMessage,\n                                                                    children: [\n                                                                        \"错误: \",\n                                                                        server.errorMessage\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-sm mt-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2.5 h-2.5 rounded-full \".concat(statusClasses[server.status]),\n                                                                                    title: \"状态: \".concat(server.status)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                    lineNumber: 286,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-theme-foreground-muted capitalize\",\n                                                                                    children: server.type\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                    lineNumber: 287,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                            lineNumber: 285,\n                                                                            columnNumber: 30\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    handleServerSelect(server.name);\n                                                                                },\n                                                                                className: \"bg-theme-primary/10 hover:bg-theme-primary/20 text-theme-primary px-3 py-1.5 rounded-full text-xs font-semibold flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                        lineNumber: 297,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            server.toolCount || 0,\n                                                                                            \" 工具\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                        lineNumber: 298,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                lineNumber: 290,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, server.name, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddServerModal__WEBPACK_IMPORTED_MODULE_2__.AddServerModal, {\n                                isOpen: showAddModal,\n                                onClose: ()=>setShowAddModal(false),\n                                newServer: newServer,\n                                onServerChange: setNewServer,\n                                onSubmit: handleAddServer\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ToolsModal__WEBPACK_IMPORTED_MODULE_3__.ToolsModal, {\n                                isOpen: showToolsModal,\n                                onClose: ()=>setShowToolsModal(false),\n                                serverName: selectedServer || '',\n                                tools: tools,\n                                onUseTool: handleUseTool,\n                                usingToolId: usingToolId,\n                                onToolUpdate: (updatedTool)=>{\n                                    // 更新工具列表中的对应工具\n                                    setTools((prevTools)=>prevTools.map((tool)=>tool.id === updatedTool.id ? updatedTool : tool));\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this),\n                            executionResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-theme-card rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-auto scrollbar-thin\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-theme-foreground\",\n                                                    children: [\n                                                        \"工具执行结果 - \",\n                                                        executionResult.toolName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setExecutionResult(null),\n                                                    className: \"text-theme-foreground-muted hover:text-theme-foreground\",\n                                                    children: \"✕\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, this),\n                                        executionResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-theme-success\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"执行成功\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-theme-background-secondary rounded-md p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"text-sm text-theme-foreground whitespace-pre-wrap\",\n                                                        children: JSON.stringify(executionResult.data, null, 2)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-theme-error\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"执行失败\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-theme-error/10 rounded-md p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-theme-error\",\n                                                        children: executionResult.error\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                open: deleteModalOpen,\n                                onClose: ()=>{\n                                    setDeleteModalOpen(false);\n                                    setServerToDelete(null);\n                                },\n                                title: \"确认删除服务器\",\n                                actions: [\n                                    {\n                                        label: '取消',\n                                        onClick: ()=>{\n                                            setDeleteModalOpen(false);\n                                            setServerToDelete(null);\n                                        },\n                                        variant: 'secondary'\n                                    },\n                                    {\n                                        label: '确认删除',\n                                        onClick: confirmDeleteServer,\n                                        variant: 'danger',\n                                        autoFocus: true\n                                    }\n                                ],\n                                width: 380,\n                                children: serverToDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"确定要删除服务器「\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                            children: serverToDelete.displayName || serverToDelete.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 28\n                                        }, this),\n                                        \"」吗？此操作不可撤销。\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(McpConfigPage, \"lI7oBw7KvjVAC6akDiOxF5NCynk=\", false, function() {\n    return [\n        _hooks_useMcpConfig__WEBPACK_IMPORTED_MODULE_1__.useMcpConfig\n    ];\n});\n_c = McpConfigPage;\nvar _c;\n$RefreshReg$(_c, \"McpConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/mcp-config/page.tsx\n"));

/***/ })

});