'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Sidebar } from './Sidebar';
import { OllamaModel } from './simple-chat/types';
import { Bot, Plus } from 'lucide-react';
import Loading from '@/components/Loading';

// 本地存储键名 - 与聊天页面保持一致
const SELECTED_MODEL_KEY = 'chat_selected_model';

export default function HomePage() {
  const router = useRouter();
  const [models, setModels] = useState<OllamaModel[]>([]);
  const [selectedModel, setSelectedModel] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // 从本地存储加载已保存的模型选择
  const loadSavedModel = useCallback(() => {
    try {
      const savedModel = localStorage.getItem(SELECTED_MODEL_KEY);
      return savedModel;
    } catch (error) {
      console.warn('无法从localStorage读取保存的模型:', error);
      return null;
    }
  }, []);

  // 保存模型选择到本地存储
  const saveModelSelection = useCallback((modelName: string) => {
    try {
      localStorage.setItem(SELECTED_MODEL_KEY, modelName);
    } catch (error) {
      console.warn('无法保存模型选择到localStorage:', error);
    }
  }, []);

  // 包装模型选择函数以添加持久化
  const handleModelChange = useCallback((modelName: string) => {
    setSelectedModel(modelName);
    saveModelSelection(modelName);
  }, [saveModelSelection]);

  // 获取模型列表
  useEffect(() => {
    const fetchModels = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/models');
        if (response.ok) {
          const data = await response.json();
          setModels(data.models || []);
          
          const savedModel = loadSavedModel();
          
          if (data.models && data.models.length > 0) {
            // 检查保存的模型是否仍然可用
            const isModelAvailable = savedModel && 
              data.models.some((model: OllamaModel) => model.name === savedModel);
            
            if (isModelAvailable) {
              // 使用保存的模型
              setSelectedModel(savedModel);
            } else {
              // 使用第一个可用模型并保存
              const firstModel = data.models[0].name;
              setSelectedModel(firstModel);
              saveModelSelection(firstModel);
            }
          }
        } else {
          setError('获取模型列表失败');
        }
      } catch (err) {
        console.error('获取模型失败:', err);
        setError('获取模型列表失败，请确保Ollama正在运行');
      } finally {
        setLoading(false);
      }
    };
    fetchModels();
  }, [loadSavedModel, saveModelSelection]);

  // 创建新对话并跳转到聊天页面
  const handleCreateConversation = async () => {
    if (!selectedModel) {
      setError('请选择一个模型');
      return;
    }

    try {
      // 创建新对话
      const title = `新对话 - ${new Date().toLocaleString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })}`;

      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          model: selectedModel,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.conversation?.id) {
          // 跳转到聊天页面，包含新对话的ID
          router.push(`/simple-chat?id=${data.conversation.id}`);
        } else {
          setError('创建对话失败');
        }
      } else {
        setError('创建对话失败');
      }
    } catch (err) {
      console.error('创建对话失败:', err);
      setError('创建对话失败');
    }
  };

  return (
    <div className="flex h-screen bg-theme-background">
      {/* 侧边栏 - 不显示对话列表，只显示导航 */}
      <Sidebar
        onCreateConversation={handleCreateConversation}
        onLoadConversation={() => {}}
        onDeleteConversation={() => {}}
      />

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col">
        {loading ? (
          <div className="flex-1 flex items-center justify-center">
            <Loading 
              size="normal"
              text="正在加载模型列表..."
              showText={true}
              containerStyle={{
                padding: '3rem'
              }}
            />
          </div>
        ) : (
          // 欢迎页面内容 - 原WelcomePage组件的内容
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Bot className="w-16 h-16 text-theme-foreground-muted mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-theme-foreground mb-2">
                Kun Agent
              </h2>
              <p className="text-theme-foreground-muted mb-6">
                智能对话助手，选择一个模型开始您的AI聊天体验
              </p>

              <div className="max-w-xs mx-auto mb-6">
                <select
                  value={selectedModel}
                  onChange={(e) => handleModelChange(e.target.value)}
                  className="w-full p-2 text-sm border border-theme-input-border rounded-md bg-theme-input text-theme-foreground focus:border-theme-input-focus focus:ring-1 focus:ring-theme-input-focus transition-colors duration-200"
                  aria-label="选择模型"
                >
                  {models.map((model) => (
                    <option key={model.name} value={model.name}>
                      {model.name}
                    </option>
                  ))}
                </select>
              </div>

              {error && (
                <div className="mb-4 p-3 bg-red-100 dark:bg-theme-error/10 border border-red-300 dark:border-theme-error/40 rounded-lg text-red-700 dark:text-red-300 text-sm max-w-md mx-auto">
                  {error}
                </div>
              )}

              {models.length === 0 ? (
                <div className="text-theme-foreground-muted">
                  <p>暂无可用模型</p>
                  <p className="text-sm mt-2">请确保 Ollama 正在运行并已安装模型</p>
                </div>
              ) : (
                <button
                  onClick={handleCreateConversation}
                  disabled={!selectedModel}
                  className="px-8 py-4 bg-theme-primary text-white rounded-lg hover:bg-theme-primary-hover disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 mx-auto transition-colors duration-200 text-lg font-medium"
                >
                  <Plus className="w-6 h-6" />
                  开始聊天
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}