"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(app-pages-browser)/./src/app/Sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_Loading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Loading */ \"(app-pages-browser)/./src/components/Loading.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// 本地存储键名 - 与聊天页面保持一致\nconst SELECTED_MODEL_KEY = 'chat_selected_model';\nfunction HomePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 从本地存储加载已保存的模型选择\n    const loadSavedModel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[loadSavedModel]\": ()=>{\n            try {\n                const savedModel = localStorage.getItem(SELECTED_MODEL_KEY);\n                return savedModel;\n            } catch (error) {\n                console.warn('无法从localStorage读取保存的模型:', error);\n                return null;\n            }\n        }\n    }[\"HomePage.useCallback[loadSavedModel]\"], []);\n    // 保存模型选择到本地存储\n    const saveModelSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[saveModelSelection]\": (modelName)=>{\n            try {\n                localStorage.setItem(SELECTED_MODEL_KEY, modelName);\n            } catch (error) {\n                console.warn('无法保存模型选择到localStorage:', error);\n            }\n        }\n    }[\"HomePage.useCallback[saveModelSelection]\"], []);\n    // 包装模型选择函数以添加持久化\n    const handleModelChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[handleModelChange]\": (modelName)=>{\n            setSelectedModel(modelName);\n            saveModelSelection(modelName);\n        }\n    }[\"HomePage.useCallback[handleModelChange]\"], [\n        saveModelSelection\n    ]);\n    // 获取模型列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const fetchModels = {\n                \"HomePage.useEffect.fetchModels\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const response = await fetch('/api/models');\n                        if (response.ok) {\n                            const data = await response.json();\n                            setModels(data.models || []);\n                            const savedModel = loadSavedModel();\n                            if (data.models && data.models.length > 0) {\n                                // 检查保存的模型是否仍然可用\n                                const isModelAvailable = savedModel && data.models.some({\n                                    \"HomePage.useEffect.fetchModels\": (model)=>model.name === savedModel\n                                }[\"HomePage.useEffect.fetchModels\"]);\n                                if (isModelAvailable) {\n                                    // 使用保存的模型\n                                    setSelectedModel(savedModel);\n                                } else {\n                                    // 使用第一个可用模型并保存\n                                    const firstModel = data.models[0].name;\n                                    setSelectedModel(firstModel);\n                                    saveModelSelection(firstModel);\n                                }\n                            }\n                        } else {\n                            setError('获取模型列表失败');\n                        }\n                    } catch (err) {\n                        console.error('获取模型失败:', err);\n                        setError('获取模型列表失败，请确保Ollama正在运行');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HomePage.useEffect.fetchModels\"];\n            fetchModels();\n        }\n    }[\"HomePage.useEffect\"], [\n        loadSavedModel,\n        saveModelSelection\n    ]);\n    // 创建新对话并跳转到聊天页面\n    const handleCreateConversation = async ()=>{\n        if (!selectedModel) {\n            setError('请选择一个模型');\n            return;\n        }\n        try {\n            // 创建新对话\n            const title = \"新对话 - \".concat(new Date().toLocaleString('zh-CN', {\n                month: 'short',\n                day: 'numeric',\n                hour: '2-digit',\n                minute: '2-digit'\n            }));\n            const response = await fetch('/api/conversations', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    title,\n                    model: selectedModel\n                })\n            });\n            if (response.ok) {\n                var _data_conversation;\n                const data = await response.json();\n                if (data.success && ((_data_conversation = data.conversation) === null || _data_conversation === void 0 ? void 0 : _data_conversation.id)) {\n                    // 跳转到聊天页面，包含新对话的ID\n                    router.push(\"/simple-chat?id=\".concat(data.conversation.id));\n                } else {\n                    setError('创建对话失败');\n                }\n            } else {\n                setError('创建对话失败');\n            }\n        } catch (err) {\n            console.error('创建对话失败:', err);\n            setError('创建对话失败');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-theme-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                onCreateConversation: handleCreateConversation,\n                onLoadConversation: ()=>{},\n                onDeleteConversation: ()=>{}\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loading__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: \"normal\",\n                        text: \"正在加载模型列表...\",\n                        showText: true,\n                        containerStyle: {\n                            padding: '3rem'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this) : // 欢迎页面内容 - 原WelcomePage组件的内容\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-16 h-16 text-theme-foreground-muted mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-theme-foreground mb-2\",\n                                children: \"Kun Agent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-theme-foreground-muted mb-6\",\n                                children: \"智能对话助手，选择一个模型开始您的AI聊天体验\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-xs mx-auto mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedModel,\n                                    onChange: (e)=>handleModelChange(e.target.value),\n                                    className: \"w-full p-2 text-sm border border-theme-input-border rounded-md bg-theme-input text-theme-foreground focus:border-theme-input-focus focus:ring-1 focus:ring-theme-input-focus transition-colors duration-200\",\n                                    \"aria-label\": \"选择模型\",\n                                    children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: model.name,\n                                            children: model.name\n                                        }, model.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-3 bg-red-100 dark:bg-theme-error/10 border border-red-300 dark:border-theme-error/40 rounded-lg text-red-700 dark:text-red-300 text-sm max-w-md mx-auto\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 17\n                            }, this),\n                            models.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-theme-foreground-muted\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"暂无可用模型\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-2\",\n                                        children: \"请确保 Ollama 正在运行并已安装模型\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCreateConversation,\n                                disabled: !selectedModel,\n                                className: \"px-8 py-4 bg-theme-primary text-white rounded-lg hover:bg-theme-primary-hover disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 mx-auto transition-colors duration-200 text-lg font-medium\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"开始聊天\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"1Ez1FIzAvpwWqYZvgaTUKitu0Kw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});