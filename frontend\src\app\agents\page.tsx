'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Loader, Plus } from 'lucide-react';
import { AgentWithRelations } from './types';
import AgentList from './components/AgentList';
import AgentFormModal from './components/AgentFormModal';
import { motion } from 'framer-motion';
import { useNotification } from '@/components/notification';
import Modal from '@/components/Modal';
import { Sidebar } from '../Sidebar';
import { Conversation } from '@/lib/database';
import { CustomModel } from '@/lib/database/custom-models';
import { McpServer, McpTool } from '@/lib/database';

export default function AgentsPage() {
  const [agents, setAgents] = useState<AgentWithRelations[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<AgentWithRelations | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [agentToDelete, setAgentToDelete] = useState<AgentWithRelations | null>(null);

  // State for modal data
  const [availableModels, setAvailableModels] = useState<CustomModel[]>([]);
  const [availableServers, setAvailableServers] = useState<McpServer[]>([]);
  const [allAvailableTools, setAllAvailableTools] = useState<McpTool[]>([]);
  const [isModalDataLoading, setIsModalDataLoading] = useState<boolean>(false);

  // 使用新的通知系统
  const notification = useNotification();

  // 移除本地对话状态，改为使用全局状态

  // 侧边栏事件处理
  const handleCreateConversation = () => {
    window.location.href = '/simple-chat?new=true';
  };

  const handleLoadConversation = (conversationId: number) => {
    window.location.href = `/simple-chat?id=${conversationId}`;
  };

  const handleDeleteConversation = async (conversationId: number) => {
    // 删除逻辑移到全局状态管理中
    // 这里可以保留为空或者调用全局删除方法
  };

  const fetchAgents = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/agents');
      if (!response.ok) {
        throw new Error('加载智能体失败');
      }
      const data = await response.json();
      setAgents(data);
    } catch (err) {
      const message = err instanceof Error ? err.message : '加载智能体时发生未知错误';
      setError(message);
      notification.error('加载失败', message);
    } finally {
      setLoading(false);
    }
  }, [notification]);

  useEffect(() => {
    fetchAgents();
  }, [fetchAgents]);

  const prepareAndOpenModal = async (agent: AgentWithRelations | null) => {
    setSelectedAgent(agent);
    setIsModalDataLoading(true);
    setIsModalOpen(true);

    try {
      const [modelsRes, serversRes, toolsRes] = await Promise.all([
        fetch('/api/custom-models'),
        fetch('/api/mcp/servers?enabled=true'),
        fetch('/api/mcp/tools?available=true')
      ]);

      if (!modelsRes.ok || !serversRes.ok || !toolsRes.ok) {
        throw new Error('加载表单数据失败');
      }

      const modelsData = await modelsRes.json();
      const serversData = await serversRes.json();
      const toolsData = await toolsRes.json();

      setAvailableModels(modelsData.models || []);
      setAvailableServers(serversData.servers || []);
      setAllAvailableTools(toolsData.tools || []);

    } catch (err) {
      const message = err instanceof Error ? err.message : '无法打开智能体编辑器';
      setError(message);
      notification.error('操作失败', message);
      setIsModalOpen(false);
    } finally {
      setIsModalDataLoading(false);
    }
  };

  const handleCreate = () => {
    prepareAndOpenModal(null);
  };

  const handleEdit = (agent: AgentWithRelations) => {
    prepareAndOpenModal(agent);
  };

  const handleDelete = (agentId: number) => {
    const agent = agents.find(a => a.id === agentId);
    if (!agent) return;
    setAgentToDelete(agent);
    setDeleteModalOpen(true);
  };

  const confirmDeleteAgent = async () => {
    if (!agentToDelete) return;
    
    try {
      setIsProcessing(true);
      const response = await fetch(`/api/agents/${agentToDelete.id}`, { 
        method: 'DELETE' 
      });
      
      if (!response.ok) {
        throw new Error('删除智能体失败');
      }
      
      await fetchAgents();
      notification.success('删除成功', `智能体 "${agentToDelete.name}" 已删除`);
    } catch (err) {
      const message = err instanceof Error ? err.message : '删除智能体失败';
      setError(message);
      notification.error('删除失败', message);
    } finally {
      setIsProcessing(false);
      setDeleteModalOpen(false);
      setAgentToDelete(null);
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedAgent(null);
  };

  const handleModalSave = () => {
    handleModalClose();
    fetchAgents();
    notification.success(
      selectedAgent ? '更新成功' : '创建成功',
      selectedAgent ? '智能体已更新' : '新智能体已创建'
    );
  };

  if (loading) {
    return (
      <div className="flex h-screen" style={{ backgroundColor: 'var(--color-background-secondary)' }}>
        <Sidebar
          onCreateConversation={handleCreateConversation}
          onLoadConversation={handleLoadConversation}
          onDeleteConversation={handleDeleteConversation}
        />
        <div className="flex-1 overflow-auto">
          <div className="steam-loading min-h-screen" style={{ backgroundColor: 'var(--color-background-secondary)' }}>
            <div className="spinner">
              <div></div>
              <div></div>
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </div>
            <p className="steam-loading-text">正在加载智能体管理...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen" style={{ backgroundColor: 'var(--color-background-secondary)' }}>
      {/* 侧边栏 */}
      <Sidebar
        onCreateConversation={handleCreateConversation}
        onLoadConversation={handleLoadConversation}
        onDeleteConversation={handleDeleteConversation}
      />
      
      {/* 主内容区域 */}
      <div className="flex-1 overflow-auto scrollbar-thin">
        <div 
          className="min-h-screen transition-all duration-300"
          style={{ backgroundColor: 'var(--color-background-secondary)' }}
        >
          {/* 页面头部 - 主标题副标题+操作区 */}
          <main className="max-w-7xl mx-auto py-8 sm:px-6 lg:px-8">
            <div className="px-4 py-6 sm:px-0">
              <div className="mb-8">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
                  <div>
                    <h1 
                      className="page-title mb-3"
                      style={{ color: 'var(--color-foreground)' }}
                    >
                      🤖 智能体管理
                    </h1>
                    <p 
                      className="page-subtitle"
                      style={{ color: 'var(--color-foreground-secondary)' }}
                    >
                      创建和管理 AI 智能体，配置专属的对话助手
                    </p>
                    <div className="mt-2 flex items-center gap-4">
                      <span 
                        className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium"
                        style={{
                          backgroundColor: 'rgba(var(--color-primary-rgb), 0.1)',
                          color: 'var(--color-primary)',
                          border: `1px solid rgba(var(--color-primary-rgb), 0.2)`
                        }}
                      >
                        共 {agents.length} 个智能体
                      </span>
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <button
                      onClick={handleCreate}
                      className="btn-primary flex items-center gap-2 px-6 py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-1"
                      style={{
                        backgroundColor: 'var(--color-primary)',
                        color: 'white',
                        background: `linear-gradient(135deg, var(--color-primary) 0%, var(--color-accent) 100%)`,
                        border: 'none'
                      }}
                    >
                      <Plus className="w-5 h-5" />
                      <span>创建智能体</span>
                    </button>
                  </div>
                </div>
              </div>
          
              {/* 主要内容区域 */}
              <motion.div 
                className="space-y-6"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              >
                {/* 处理状态指示器 */}
                {isProcessing && (
                  <div className="fixed top-20 left-1/2 transform -translate-x-1/2 z-40">
                    <div 
                      className="rounded-xl px-6 py-3 shadow-lg flex items-center gap-3 backdrop-blur-sm"
                      style={{
                        backgroundColor: 'var(--color-card)',
                        border: `1px solid var(--color-border)`,
                        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)'
                      }}
                    >
                      <div className="spinner-small">
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                      </div>
                      <span className="text-sm font-medium" style={{ color: 'var(--color-foreground)' }}>
                        处理中...
                      </span>
                    </div>
                  </div>
                )}

                {/* 错误提示 */}
                {error && !loading && (
                  <div 
                    className="mb-6 p-6 rounded-xl border"
                    style={{
                      backgroundColor: 'rgba(var(--color-error), 0.05)',
                      borderColor: 'rgba(var(--color-error), 0.2)',
                      color: 'var(--color-error)'
                    }}
                  >
                    <p className="font-semibold mb-2">⚠️ 加载出错</p>
                    <p className="text-sm mb-3 opacity-90">{error}</p>
                    <button 
                      onClick={fetchAgents}
                      className="inline-flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:-translate-y-0.5"
                      style={{
                        backgroundColor: 'var(--color-error)',
                        color: 'white',
                        border: 'none'
                      }}
                    >
                      🔄 重试
                    </button>
                  </div>
                )}

                {/* 智能体列表 */}
                <div className="space-y-6">
                  <AgentList
                    agents={agents}
                    isLoading={loading}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                  />
                </div>
              </motion.div>

              {/* 智能体表单弹窗 */}
              {isModalOpen && (
                isModalDataLoading ? (
                  <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
                    <div 
                      className="rounded-2xl p-8 flex flex-col items-center gap-6 shadow-2xl"
                      style={{
                        backgroundColor: 'var(--color-card)',
                        border: `1px solid var(--color-border)`,
                        background: `linear-gradient(135deg, 
                          var(--color-card) 0%, 
                          var(--color-background-secondary) 100%)`
                      }}
                    >
                      <div className="spinner">
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                      </div>
                      <p className="text-sm font-medium" style={{ color: 'var(--color-foreground)' }}>
                        正在加载表单数据...
                      </p>
                    </div>
                  </div>
                ) : (
                  <AgentFormModal
                    agent={selectedAgent}
                    onClose={handleModalClose}
                    onSave={handleModalSave}
                    availableModels={availableModels}
                    availableServers={availableServers}
                    allAvailableTools={allAvailableTools}
                  />
                )
              )}

              {/* 删除确认弹窗 */}
              <Modal
                open={deleteModalOpen}
                onClose={() => { setDeleteModalOpen(false); setAgentToDelete(null); }}
                title="确认删除智能体"
                icon={<Loader className="w-6 h-6 text-theme-warning" />}
                actions={[
                  {
                    label: '取消',
                    onClick: () => { setDeleteModalOpen(false); setAgentToDelete(null); },
                    variant: 'secondary',
                  },
                  {
                    label: '确认删除',
                    onClick: confirmDeleteAgent,
                    variant: 'danger',
                    autoFocus: true,
                  },
                ]}
                width={380}
              >
                {agentToDelete && (
                  <span>
                    确定要删除智能体「<b>{agentToDelete.name}</b>」吗？此操作不可撤销。
                  </span>
                )}
              </Modal>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
} 