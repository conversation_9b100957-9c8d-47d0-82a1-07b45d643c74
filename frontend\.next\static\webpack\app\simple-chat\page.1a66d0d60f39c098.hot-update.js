"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-chat/page",{

/***/ "(app-pages-browser)/./src/app/simple-chat/hooks/useConversationManager.ts":
/*!*************************************************************!*\
  !*** ./src/app/simple-chat/hooks/useConversationManager.ts ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConversationManager: () => (/* binding */ useConversationManager)\n/* harmony export */ });\nfunction useConversationManager() {\n    const [conversations, setConversations] = useState([]);\n    const [currentConversation, setCurrentConversation] = useState(null);\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState(null);\n    // 加载对话列表\n    const loadConversations = useCallback({\n        \"useConversationManager.useCallback[loadConversations]\": async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                const response = await fetch('/api/conversations');\n                const data = await response.json();\n                if (data.success) {\n                    setConversations(data.conversations || []);\n                } else {\n                    setError(data.error || '加载对话列表失败');\n                }\n            } catch (err) {\n                setError('网络错误，加载对话列表失败');\n                console.error('加载对话列表失败:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useConversationManager.useCallback[loadConversations]\"], []);\n    // 创建新对话\n    const createConversation = useCallback({\n        \"useConversationManager.useCallback[createConversation]\": async (title, model)=>{\n            try {\n                setError(null);\n                console.log(\"[useConversationManager] 开始创建对话: \".concat(title, \", 模型: \").concat(model));\n                const response = await fetch('/api/conversations', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        title,\n                        model\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[useConversationManager] 对话创建成功:\", data.conversation);\n                    // 刷新对话列表\n                    await loadConversations();\n                    console.log(\"[useConversationManager] 对话列表已刷新\");\n                    // 切换到新创建的对话\n                    setCurrentConversation(data.conversation);\n                    console.log(\"[useConversationManager] 当前对话已设置为新创建的对话:\", data.conversation);\n                    return data.conversation.id;\n                } else {\n                    console.error(\"[useConversationManager] 创建对话失败:\", data.error);\n                    setError(data.error || '创建对话失败');\n                    return null;\n                }\n            } catch (err) {\n                console.error(\"[useConversationManager] 创建对话异常:\", err);\n                setError('网络错误，创建对话失败');\n                return null;\n            }\n        }\n    }[\"useConversationManager.useCallback[createConversation]\"], [\n        loadConversations\n    ]);\n    // 切换对话\n    const switchConversation = useCallback({\n        \"useConversationManager.useCallback[switchConversation]\": async (id)=>{\n            try {\n                setError(null);\n                console.log(\"[useConversationManager] 开始切换到对话 \".concat(id));\n                const response = await fetch(\"/api/conversations/\".concat(id));\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[useConversationManager] 成功获取对话 \".concat(id, \" 信息:\"), data.conversation);\n                    setCurrentConversation(data.conversation);\n                    console.log(\"[useConversationManager] 当前对话状态已更新为:\", data.conversation);\n                } else {\n                    console.error(\"[useConversationManager] 切换对话 \".concat(id, \" 失败:\"), data.error);\n                    setError(data.error || '切换对话失败');\n                    throw new Error(data.error || '切换对话失败');\n                }\n            } catch (err) {\n                console.error(\"[useConversationManager] 切换对话 \".concat(id, \" 异常:\"), err);\n                setError('网络错误，切换对话失败');\n                throw err;\n            }\n        }\n    }[\"useConversationManager.useCallback[switchConversation]\"], []);\n    // 删除对话\n    const deleteConversation = useCallback({\n        \"useConversationManager.useCallback[deleteConversation]\": async (id)=>{\n            try {\n                setError(null);\n                if (!confirm('确定要删除这个对话吗？此操作无法撤销。')) {\n                    return;\n                }\n                const response = await fetch(\"/api/conversations/\".concat(id), {\n                    method: 'DELETE'\n                });\n                const data = await response.json();\n                if (data.success) {\n                    // 如果删除的是当前对话，清空当前对话\n                    if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === id) {\n                        setCurrentConversation(null);\n                    }\n                    // 刷新对话列表\n                    await loadConversations();\n                } else {\n                    setError(data.error || '删除对话失败');\n                }\n            } catch (err) {\n                setError('网络错误，删除对话失败');\n                console.error('删除对话失败:', err);\n            }\n        }\n    }[\"useConversationManager.useCallback[deleteConversation]\"], [\n        currentConversation,\n        loadConversations\n    ]);\n    // 更新对话标题\n    const updateConversationTitle = useCallback({\n        \"useConversationManager.useCallback[updateConversationTitle]\": async (id, title)=>{\n            try {\n                setError(null);\n                const response = await fetch(\"/api/conversations/\".concat(id), {\n                    method: 'PATCH',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        title\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    // 更新本地状态\n                    setConversations({\n                        \"useConversationManager.useCallback[updateConversationTitle]\": (prev)=>prev.map({\n                                \"useConversationManager.useCallback[updateConversationTitle]\": (conv)=>conv.id === id ? {\n                                        ...conv,\n                                        title\n                                    } : conv\n                            }[\"useConversationManager.useCallback[updateConversationTitle]\"])\n                    }[\"useConversationManager.useCallback[updateConversationTitle]\"]);\n                    // 如果是当前对话，也更新当前对话状态\n                    if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === id) {\n                        setCurrentConversation({\n                            \"useConversationManager.useCallback[updateConversationTitle]\": (prev)=>prev ? {\n                                    ...prev,\n                                    title\n                                } : null\n                        }[\"useConversationManager.useCallback[updateConversationTitle]\"]);\n                    }\n                } else {\n                    setError(data.error || '更新对话标题失败');\n                }\n            } catch (err) {\n                setError('网络错误，更新对话标题失败');\n                console.error('更新对话标题失败:', err);\n            }\n        }\n    }[\"useConversationManager.useCallback[updateConversationTitle]\"], [\n        currentConversation\n    ]);\n    // 初始化时加载对话列表\n    useEffect({\n        \"useConversationManager.useEffect\": ()=>{\n            loadConversations();\n        }\n    }[\"useConversationManager.useEffect\"], [\n        loadConversations\n    ]);\n    return {\n        conversations,\n        currentConversation,\n        loading,\n        error,\n        loadConversations,\n        createConversation,\n        switchConversation,\n        deleteConversation,\n        updateConversationTitle\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/simple-chat/hooks/useConversationManager.ts\n"));

/***/ })

});