{"/api/mcp/server-list/route": "app/api/mcp/server-list/route.js", "/api/custom-models/route": "app/api/custom-models/route.js", "/api/models/route": "app/api/models/route.js", "/api/agents/route": "app/api/agents/route.js", "/api/mcp/tools/route": "app/api/mcp/tools/route.js", "/api/conversations/[id]/route": "app/api/conversations/[id]/route.js", "/api/conversations/route": "app/api/conversations/route.js", "/agents/page": "app/agents/page.js", "/mcp-config/page": "app/mcp-config/page.js", "/model-manager/page": "app/model-manager/page.js", "/simple-chat/page": "app/simple-chat/page.js", "/page": "app/page.js", "/conversations/page": "app/conversations/page.js"}