{"/api/conversations/route": "app/api/conversations/route.js", "/api/models/route": "app/api/models/route.js", "/api/mcp/server-list/route": "app/api/mcp/server-list/route.js", "/api/agents/route": "app/api/agents/route.js", "/api/custom-models/route": "app/api/custom-models/route.js", "/page": "app/page.js", "/mcp-config/page": "app/mcp-config/page.js", "/model-manager/page": "app/model-manager/page.js", "/agents/page": "app/agents/page.js", "/simple-chat/page": "app/simple-chat/page.js"}