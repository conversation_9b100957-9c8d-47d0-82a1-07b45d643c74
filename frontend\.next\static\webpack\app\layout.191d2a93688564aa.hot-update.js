/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CConversationContext.tsx%22%2C%22ids%22%3A%5B%22ConversationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=false!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CConversationContext.tsx%22%2C%22ids%22%3A%5B%22ConversationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=false! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/examples/NotificationDemo.tsx */ \"(app-pages-browser)/./src/components/notification/examples/NotificationDemo.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationContainer.tsx */ \"(app-pages-browser)/./src/components/notification/NotificationContainer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationContext.tsx */ \"(app-pages-browser)/./src/components/notification/NotificationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationItem.tsx */ \"(app-pages-browser)/./src/components/notification/NotificationItem.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/notification/NotificationManager.tsx */ \"(app-pages-browser)/./src/components/notification/NotificationManager.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ConversationContext.tsx */ \"(app-pages-browser)/./src/contexts/ConversationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/theme/components/ColorThemeScript.tsx */ \"(app-pages-browser)/./src/theme/components/ColorThemeScript.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/theme/contexts/ThemeContext.tsx */ \"(app-pages-browser)/./src/theme/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5Cexamples%5C%5CNotificationDemo.tsx%22%2C%22ids%22%3A%5B%22NotificationDemo%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContainer.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationContext.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationItem.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cnotification%5C%5CNotificationManager.tsx%22%2C%22ids%22%3A%5B%22*%22%2C%22NotificationManager%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CConversationContext.tsx%22%2C%22ids%22%3A%5B%22ConversationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccomponents%5C%5CColorThemeScript.tsx%22%2C%22ids%22%3A%5B%22ColorThemeScript%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ctheme%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/ConversationContext.tsx":
/*!**********************************************!*\
  !*** ./src/contexts/ConversationContext.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationProvider: () => (/* binding */ ConversationProvider),\n/* harmony export */   useConversations: () => (/* binding */ useConversations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ConversationProvider,useConversations auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ConversationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// 全局缓存配置\nconst CACHE_DURATION = 30000; // 30秒缓存\nlet globalConversationsCache = [];\nlet globalCacheTimestamp = 0;\nlet isLoadingGlobal = false;\nfunction ConversationProvider(param) {\n    let { children } = param;\n    _s();\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 防止重复请求的引用\n    const loadingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 检查缓存是否有效\n    const isCacheValid = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[isCacheValid]\": ()=>{\n            const now = Date.now();\n            return globalConversationsCache.length > 0 && now - globalCacheTimestamp < CACHE_DURATION;\n        }\n    }[\"ConversationProvider.useCallback[isCacheValid]\"], []);\n    // 加载对话列表\n    const loadConversations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[loadConversations]\": async function() {\n            let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n            // 如果正在加载中，直接返回\n            if (loadingRef.current || isLoadingGlobal) {\n                return;\n            }\n            // 如果不强制刷新且缓存有效，使用缓存\n            if (!forceRefresh && isCacheValid()) {\n                setConversations(globalConversationsCache);\n                return;\n            }\n            try {\n                loadingRef.current = true;\n                isLoadingGlobal = true;\n                setLoading(true);\n                setError(null);\n                // 取消之前的请求\n                if (abortControllerRef.current) {\n                    abortControllerRef.current.abort();\n                }\n                // 创建新的AbortController\n                abortControllerRef.current = new AbortController();\n                console.log('[ConversationProvider] 开始加载对话列表...');\n                const response = await fetch('/api/conversations', {\n                    signal: abortControllerRef.current.signal\n                });\n                const data = await response.json();\n                if (data.success) {\n                    const conversationList = data.conversations || [];\n                    // 更新全局缓存\n                    globalConversationsCache = conversationList;\n                    globalCacheTimestamp = Date.now();\n                    setConversations(conversationList);\n                    console.log(\"[ConversationProvider] 成功加载 \".concat(conversationList.length, \" 个对话\"));\n                } else {\n                    setError(data.error || '加载对话列表失败');\n                }\n            } catch (err) {\n                if (err instanceof Error && err.name === 'AbortError') {\n                    console.log('[ConversationProvider] 请求被取消');\n                    return;\n                }\n                setError('网络错误，加载对话列表失败');\n                console.error('加载对话列表失败:', err);\n            } finally{\n                loadingRef.current = false;\n                isLoadingGlobal = false;\n                setLoading(false);\n                abortControllerRef.current = null;\n            }\n        }\n    }[\"ConversationProvider.useCallback[loadConversations]\"], [\n        isCacheValid\n    ]);\n    // 创建新对话\n    const createConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[createConversation]\": async (title, model)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始创建对话: \".concat(title, \", 模型: \").concat(model));\n                const response = await fetch('/api/conversations', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        title,\n                        model\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 对话创建成功:\", data.conversation);\n                    // 刷新对话列表\n                    await loadConversations(true);\n                    // 切换到新创建的对话\n                    setCurrentConversation(data.conversation);\n                    return data.conversation.id;\n                } else {\n                    console.error(\"[ConversationProvider] 创建对话失败:\", data.error);\n                    setError(data.error || '创建对话失败');\n                    return null;\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 创建对话异常:\", err);\n                setError('网络错误，创建对话失败');\n                return null;\n            }\n        }\n    }[\"ConversationProvider.useCallback[createConversation]\"], [\n        loadConversations\n    ]);\n    // 切换对话\n    const switchConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[switchConversation]\": async (id)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始切换到对话 \".concat(id));\n                const response = await fetch(\"/api/conversations/\".concat(id));\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 成功获取对话 \".concat(id, \" 信息:\"), data.conversation);\n                    setCurrentConversation(data.conversation);\n                } else {\n                    console.error(\"[ConversationProvider] 切换对话 \".concat(id, \" 失败:\"), data.error);\n                    setError(data.error || '切换对话失败');\n                    throw new Error(data.error || '切换对话失败');\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 切换对话 \".concat(id, \" 异常:\"), err);\n                setError('网络错误，切换对话失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[switchConversation]\"], []);\n    // 删除对话\n    const deleteConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[deleteConversation]\": async (id)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始删除对话 \".concat(id));\n                const response = await fetch(\"/api/conversations/\".concat(id), {\n                    method: 'DELETE'\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 对话 \".concat(id, \" 删除成功\"));\n                    // 从本地状态中移除\n                    setConversations({\n                        \"ConversationProvider.useCallback[deleteConversation]\": (prev)=>prev.filter({\n                                \"ConversationProvider.useCallback[deleteConversation]\": (conv)=>conv.id !== id\n                            }[\"ConversationProvider.useCallback[deleteConversation]\"])\n                    }[\"ConversationProvider.useCallback[deleteConversation]\"]);\n                    // 更新全局缓存\n                    globalConversationsCache = globalConversationsCache.filter({\n                        \"ConversationProvider.useCallback[deleteConversation]\": (conv)=>conv.id !== id\n                    }[\"ConversationProvider.useCallback[deleteConversation]\"]);\n                    // 如果删除的是当前对话，清空当前对话\n                    if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === id) {\n                        setCurrentConversation(null);\n                    }\n                } else {\n                    console.error(\"[ConversationProvider] 删除对话 \".concat(id, \" 失败:\"), data.error);\n                    setError(data.error || '删除对话失败');\n                    throw new Error(data.error || '删除对话失败');\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 删除对话 \".concat(id, \" 异常:\"), err);\n                setError('网络错误，删除对话失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[deleteConversation]\"], [\n        currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id\n    ]);\n    // 更新对话标题\n    const updateConversationTitle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConversationProvider.useCallback[updateConversationTitle]\": async (id, title)=>{\n            try {\n                setError(null);\n                console.log(\"[ConversationProvider] 开始更新对话 \".concat(id, \" 标题: \").concat(title));\n                const response = await fetch(\"/api/conversations/\".concat(id), {\n                    method: 'PATCH',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        title\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    console.log(\"[ConversationProvider] 对话 \".concat(id, \" 标题更新成功\"));\n                    // 更新本地状态\n                    setConversations({\n                        \"ConversationProvider.useCallback[updateConversationTitle]\": (prev)=>prev.map({\n                                \"ConversationProvider.useCallback[updateConversationTitle]\": (conv)=>conv.id === id ? {\n                                        ...conv,\n                                        title\n                                    } : conv\n                            }[\"ConversationProvider.useCallback[updateConversationTitle]\"])\n                    }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    // 更新全局缓存\n                    globalConversationsCache = globalConversationsCache.map({\n                        \"ConversationProvider.useCallback[updateConversationTitle]\": (conv)=>conv.id === id ? {\n                                ...conv,\n                                title\n                            } : conv\n                    }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    // 如果是当前对话，也更新当前对话状态\n                    if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === id) {\n                        setCurrentConversation({\n                            \"ConversationProvider.useCallback[updateConversationTitle]\": (prev)=>prev ? {\n                                    ...prev,\n                                    title\n                                } : null\n                        }[\"ConversationProvider.useCallback[updateConversationTitle]\"]);\n                    }\n                } else {\n                    console.error(\"[ConversationProvider] 更新对话 \".concat(id, \" 标题失败:\"), data.error);\n                    setError(data.error || '更新对话标题失败');\n                    throw new Error(data.error || '更新对话标题失败');\n                }\n            } catch (err) {\n                console.error(\"[ConversationProvider] 更新对话 \".concat(id, \" 标题异常:\"), err);\n                setError('网络错误，更新对话标题失败');\n                throw err;\n            }\n        }\n    }[\"ConversationProvider.useCallback[updateConversationTitle]\"], [\n        currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id\n    ]);\n    // 初始化时加载对话列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationProvider.useEffect\": ()=>{\n            loadConversations();\n        }\n    }[\"ConversationProvider.useEffect\"], [\n        loadConversations\n    ]);\n    // 组件卸载时清理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationProvider.useEffect\": ()=>{\n            return ({\n                \"ConversationProvider.useEffect\": ()=>{\n                    if (abortControllerRef.current) {\n                        abortControllerRef.current.abort();\n                    }\n                }\n            })[\"ConversationProvider.useEffect\"];\n        }\n    }[\"ConversationProvider.useEffect\"], []);\n    const value = {\n        conversations,\n        currentConversation,\n        loading,\n        error,\n        loadConversations,\n        createConversation,\n        switchConversation,\n        deleteConversation,\n        updateConversationTitle,\n        setCurrentConversation\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConversationContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\contexts\\\\ConversationContext.tsx\",\n        lineNumber: 286,\n        columnNumber: 5\n    }, this);\n}\n_s(ConversationProvider, \"eDx1BKB9zBJkVZ1FPc7VJppvBuo=\");\n_c = ConversationProvider;\nfunction useConversations() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ConversationContext);\n    if (context === undefined) {\n        throw new Error('useConversations must be used within a ConversationProvider');\n    }\n    return context;\n}\n_s1(useConversations, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ConversationProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ConversationContext.tsx\n"));

/***/ })

});