"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/mcp-config/page",{

/***/ "(app-pages-browser)/./src/app/mcp-config/page.tsx":
/*!*************************************!*\
  !*** ./src/app/mcp-config/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ McpConfigPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _hooks_useMcpConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hooks/useMcpConfig */ \"(app-pages-browser)/./src/app/mcp-config/hooks/useMcpConfig.ts\");\n/* harmony import */ var _components_AddServerModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/AddServerModal */ \"(app-pages-browser)/./src/app/mcp-config/components/AddServerModal.tsx\");\n/* harmony import */ var _components_ToolsModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ToolsModal */ \"(app-pages-browser)/./src/app/mcp-config/components/ToolsModal.tsx\");\n/* harmony import */ var _barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Axe,Plus,RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Axe,Plus,RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Axe,Plus,RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Axe,Plus,RefreshCw,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/axe.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_notification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/notification */ \"(app-pages-browser)/./src/components/notification/index.ts\");\n/* harmony import */ var _components_Modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Modal */ \"(app-pages-browser)/./src/components/Modal.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../Sidebar */ \"(app-pages-browser)/./src/app/Sidebar.tsx\");\n/* harmony import */ var _components_Loading__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Loading */ \"(app-pages-browser)/./src/components/Loading.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction McpConfigPage() {\n    _s();\n    const { servers, tools, loading, toolsLoading, showAddModal, showToolsModal, selectedTab, selectedServer, newServer, setShowAddModal, setShowToolsModal, setNewServer, setTools, loadServers, loadTools, handleTabChange, handleServerSelect, checkServerStatus, refreshTools, handleDeleteTool, handleUseTool, handleAddServer, handleDeleteServer, executionResult, setExecutionResult, usingToolId } = (0,_hooks_useMcpConfig__WEBPACK_IMPORTED_MODULE_1__.useMcpConfig)();\n    // 操作类弹窗\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [serverToDelete, setServerToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    var _useNotification;\n    const notification = (_useNotification = _components_notification__WEBPACK_IMPORTED_MODULE_5__.useNotification === null || _components_notification__WEBPACK_IMPORTED_MODULE_5__.useNotification === void 0 ? void 0 : (0,_components_notification__WEBPACK_IMPORTED_MODULE_5__.useNotification)()) !== null && _useNotification !== void 0 ? _useNotification : null;\n    // 移除本地对话状态，改为使用全局状态\n    // 侧边栏事件处理\n    const handleCreateConversation = ()=>{\n        window.location.href = '/simple-chat?new=true';\n    };\n    const handleLoadConversation = (conversationId)=>{\n        window.location.href = \"/simple-chat?id=\".concat(conversationId);\n    };\n    const handleDeleteConversation = async (conversationId)=>{\n        try {\n            const response = await fetch(\"/api/conversations/\".concat(conversationId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                var _currentConversation;\n                setConversations((prev)=>prev.filter((conv)=>conv.id !== conversationId));\n                if (((_currentConversation = currentConversation) === null || _currentConversation === void 0 ? void 0 : _currentConversation.id) === conversationId) {\n                    setCurrentConversation(null);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to delete conversation:', error);\n        }\n    };\n    // 删除服务器弹窗触发\n    const handleDeleteServerModal = (server)=>{\n        setServerToDelete(server);\n        setDeleteModalOpen(true);\n    };\n    // 确认删除服务器\n    const confirmDeleteServer = async ()=>{\n        if (!serverToDelete) return;\n        try {\n            // 本地服务器不允许删除\n            if (serverToDelete.name === 'local') {\n                var _notification_error;\n                notification && ((_notification_error = notification.error) === null || _notification_error === void 0 ? void 0 : _notification_error.call(notification, '本地服务器不支持删除操作'));\n                setDeleteModalOpen(false);\n                setServerToDelete(null);\n                return;\n            }\n            // 先删除数据库记录\n            const response = await fetch(\"/api/mcp/servers/\".concat(serverToDelete.id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                // 然后删除配置文件中的记录\n                const configResponse = await fetch('/api/mcp/config', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'delete',\n                        serverName: serverToDelete.name\n                    })\n                });\n                if (configResponse.ok) {\n                    var _notification_success;\n                    notification && ((_notification_success = notification.success) === null || _notification_success === void 0 ? void 0 : _notification_success.call(notification, '服务器删除成功', \"服务器「\".concat(serverToDelete.displayName || serverToDelete.name, \"」已删除\")));\n                    if (selectedServer === serverToDelete.name) {\n                        handleServerSelect(null);\n                        setTools([]);\n                    }\n                    await loadServers();\n                } else {\n                    var _notification_error1;\n                    const configErrorData = await configResponse.json();\n                    notification && ((_notification_error1 = notification.error) === null || _notification_error1 === void 0 ? void 0 : _notification_error1.call(notification, '删除配置文件失败', configErrorData.error || '未知错误'));\n                }\n            } else {\n                var _notification_error2;\n                const errorData = await response.json();\n                notification && ((_notification_error2 = notification.error) === null || _notification_error2 === void 0 ? void 0 : _notification_error2.call(notification, '删除服务器失败', errorData.error || '未知错误'));\n            }\n        } catch (error) {\n            var _notification_error3;\n            notification && ((_notification_error3 = notification.error) === null || _notification_error3 === void 0 ? void 0 : _notification_error3.call(notification, '删除服务器失败', error instanceof Error ? error.message : '未知错误'));\n        } finally{\n            setDeleteModalOpen(false);\n            setServerToDelete(null);\n        }\n    };\n    const statusClasses = {\n        connected: 'bg-theme-success',\n        error: 'bg-theme-error',\n        disconnected: 'bg-theme-foreground-muted',\n        connecting: 'bg-yellow-500'\n    };\n    const borderClasses = {\n        connecting: 'ring-2 ring-yellow-500/50 ring-offset-2 ring-offset-theme-background-secondary animate-pulse',\n        selected: 'ring-2 ring-theme-primary ring-offset-2 ring-offset-theme-background-secondary',\n        default: 'border-theme-border hover:border-theme-border-secondary'\n    };\n    const getBorderClass = (server)=>{\n        if (server.status === 'connecting') return borderClasses.connecting;\n        if (selectedServer === server.name) return borderClasses.selected;\n        return borderClasses.default;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notification__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-screen bg-theme-background-secondary\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_7__.Sidebar, {\n                        conversations: conversations,\n                        currentConversation: currentConversation,\n                        onCreateConversation: handleCreateConversation,\n                        onLoadConversation: handleLoadConversation,\n                        onDeleteConversation: handleDeleteConversation\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loading__WEBPACK_IMPORTED_MODULE_8__.PageLoading, {\n                            text: \"正在加载MCP服务器配置...\",\n                            fullScreen: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notification__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-theme-background-secondary\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_7__.Sidebar, {\n                    conversations: conversations,\n                    currentConversation: currentConversation,\n                    onCreateConversation: handleCreateConversation,\n                    onLoadConversation: handleLoadConversation,\n                    onDeleteConversation: handleDeleteConversation\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-auto scrollbar-thin\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen bg-theme-background-secondary transition-colors duration-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notification__WEBPACK_IMPORTED_MODULE_5__.NotificationManager, {\n                                position: \"top-right\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"max-w-7xl mx-auto py-8 sm:px-6 lg:px-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-6 sm:px-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"page-title\",\n                                                                children: \"\\uD83C\\uDF10MCP 服务器配置\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"page-subtitle mt-2\",\n                                                                children: \"管理模型上下文协议服务器，配置工具和连接\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowAddModal(true),\n                                                            className: \"inline-flex items-center gap-2 px-4 py-2 bg-theme-primary text-white rounded-lg hover:bg-theme-primary-hover transition-colors duration-200 font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"添加服务器\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-b border-theme-border\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                    className: \"-mb-px flex space-x-8\",\n                                                    children: [\n                                                        {\n                                                            key: 'all',\n                                                            label: '全部',\n                                                            count: servers.length\n                                                        },\n                                                        {\n                                                            key: 'local',\n                                                            label: '本地服务器',\n                                                            count: servers.filter((s)=>s.type === 'stdio').length\n                                                        },\n                                                        {\n                                                            key: 'external',\n                                                            label: '外部服务器',\n                                                            count: servers.filter((s)=>s.type !== 'stdio').length\n                                                        }\n                                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleTabChange(tab.key),\n                                                            className: \"\".concat(selectedTab === tab.key ? 'border-theme-primary text-theme-primary' : 'border-transparent text-theme-foreground-muted hover:text-theme-foreground hover:border-theme-border-secondary', \" whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors duration-200 focus:outline-none\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tab.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-theme-background-tertiary text-theme-foreground-secondary py-0.5 px-2.5 rounded-full text-xs font-medium\",\n                                                                    children: tab.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, tab.key, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                    children: servers.filter((server)=>{\n                                                        if (selectedTab === 'all') return true;\n                                                        if (selectedTab === 'local') return server.type === 'stdio';\n                                                        if (selectedTab === 'external') return server.type !== 'stdio';\n                                                        return true;\n                                                    }).map((server)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-theme-card border rounded-lg p-4 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 \".concat(getBorderClass(server)),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start justify-between mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-base font-semibold text-theme-foreground truncate\",\n                                                                                children: server.displayName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                            lineNumber: 262,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-1 pl-4\",\n                                                                            children: [\n                                                                                server.type !== 'stdio' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        checkServerStatus(server.name);\n                                                                                    },\n                                                                                    className: \"text-theme-foreground-muted hover:text-theme-primary p-1.5 rounded-full hover:bg-theme-primary/10 transition-colors\",\n                                                                                    title: \"检查连接状态\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"w-3.5 h-3.5 \".concat(server.status === 'connecting' ? 'animate-spin' : '')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                        lineNumber: 275,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                    lineNumber: 267,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                server.name !== 'local' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        handleDeleteServerModal(server);\n                                                                                    },\n                                                                                    className: \"text-theme-foreground-muted hover:text-theme-error p-1.5 rounded-full hover:bg-theme-error/10 transition-colors\",\n                                                                                    title: \"删除服务器\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                        className: \"w-3.5 h-3.5\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                        lineNumber: 287,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                    lineNumber: 279,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                            lineNumber: 265,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-theme-foreground-muted my-3 h-10 overflow-hidden\",\n                                                                    children: server.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                server.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-theme-error mb-2 truncate\",\n                                                                    title: server.errorMessage,\n                                                                    children: [\n                                                                        \"错误: \",\n                                                                        server.errorMessage\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-sm mt-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2.5 h-2.5 rounded-full \".concat(statusClasses[server.status]),\n                                                                                    title: \"状态: \".concat(server.status)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                    lineNumber: 300,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-theme-foreground-muted capitalize\",\n                                                                                    children: server.type\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                    lineNumber: 301,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 30\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    handleServerSelect(server.name);\n                                                                                },\n                                                                                className: \"bg-theme-primary/10 hover:bg-theme-primary/20 text-theme-primary px-3 py-1.5 rounded-full text-xs font-semibold flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Axe_Plus_RefreshCw_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                        lineNumber: 311,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            server.toolCount || 0,\n                                                                                            \" 工具\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                        lineNumber: 312,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                                lineNumber: 304,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, server.name, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddServerModal__WEBPACK_IMPORTED_MODULE_2__.AddServerModal, {\n                                isOpen: showAddModal,\n                                onClose: ()=>setShowAddModal(false),\n                                newServer: newServer,\n                                onServerChange: setNewServer,\n                                onSubmit: handleAddServer\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ToolsModal__WEBPACK_IMPORTED_MODULE_3__.ToolsModal, {\n                                isOpen: showToolsModal,\n                                onClose: ()=>setShowToolsModal(false),\n                                serverName: selectedServer || '',\n                                tools: tools,\n                                onUseTool: handleUseTool,\n                                usingToolId: usingToolId,\n                                onToolUpdate: (updatedTool)=>{\n                                    // 更新工具列表中的对应工具\n                                    setTools((prevTools)=>prevTools.map((tool)=>tool.id === updatedTool.id ? updatedTool : tool));\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this),\n                            executionResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-theme-card rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-auto scrollbar-thin\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-theme-foreground\",\n                                                    children: [\n                                                        \"工具执行结果 - \",\n                                                        executionResult.toolName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setExecutionResult(null),\n                                                    className: \"text-theme-foreground-muted hover:text-theme-foreground\",\n                                                    children: \"✕\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 19\n                                        }, this),\n                                        executionResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-theme-success\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"执行成功\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-theme-background-secondary rounded-md p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"text-sm text-theme-foreground whitespace-pre-wrap\",\n                                                        children: JSON.stringify(executionResult.data, null, 2)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-theme-error\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"执行失败\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-theme-error/10 rounded-md p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-theme-error\",\n                                                        children: executionResult.error\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                open: deleteModalOpen,\n                                onClose: ()=>{\n                                    setDeleteModalOpen(false);\n                                    setServerToDelete(null);\n                                },\n                                title: \"确认删除服务器\",\n                                actions: [\n                                    {\n                                        label: '取消',\n                                        onClick: ()=>{\n                                            setDeleteModalOpen(false);\n                                            setServerToDelete(null);\n                                        },\n                                        variant: 'secondary'\n                                    },\n                                    {\n                                        label: '确认删除',\n                                        onClick: confirmDeleteServer,\n                                        variant: 'danger',\n                                        autoFocus: true\n                                    }\n                                ],\n                                width: 380,\n                                children: serverToDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"确定要删除服务器「\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                            children: serverToDelete.displayName || serverToDelete.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 28\n                                        }, this),\n                                        \"」吗？此操作不可撤销。\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n_s(McpConfigPage, \"lI7oBw7KvjVAC6akDiOxF5NCynk=\", false, function() {\n    return [\n        _hooks_useMcpConfig__WEBPACK_IMPORTED_MODULE_1__.useMcpConfig\n    ];\n});\n_c = McpConfigPage;\nvar _c;\n$RefreshReg$(_c, \"McpConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/mcp-config/page.tsx\n"));

/***/ })

});