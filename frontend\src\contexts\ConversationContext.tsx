'use client';

import React, { createContext, useContext, useState, useCallback, useRef, useEffect } from 'react';
import { Conversation } from '@/lib/database/types';
import { apiRequest, apiRequestManager } from '@/utils/apiRequestManager';

interface ConversationContextType {
  conversations: Conversation[];
  currentConversation: Conversation | null;
  loading: boolean;
  error: string | null;
  
  // 操作函数
  loadConversations: (forceRefresh?: boolean) => Promise<void>;
  createConversation: (title: string, model: string) => Promise<number | null>;
  switchConversation: (id: number) => Promise<void>;
  deleteConversation: (id: number) => Promise<void>;
  updateConversationTitle: (id: number, title: string) => Promise<void>;
  setCurrentConversation: (conversation: Conversation | null) => void;
}

const ConversationContext = createContext<ConversationContextType | undefined>(undefined);

// 全局缓存配置
const CACHE_DURATION = 30000; // 30秒缓存
let globalConversationsCache: Conversation[] = [];
let globalCacheTimestamp = 0;
let isLoadingGlobal = false;

interface ConversationProviderProps {
  children: React.ReactNode;
}

export function ConversationProvider({ children }: ConversationProviderProps) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 防止重复请求的引用
  const loadingRef = useRef(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 检查缓存是否有效
  const isCacheValid = useCallback(() => {
    const now = Date.now();
    return globalConversationsCache.length > 0 && 
           (now - globalCacheTimestamp) < CACHE_DURATION;
  }, []);

  // 加载对话列表
  const loadConversations = useCallback(async (forceRefresh = false) => {
    // 如果正在加载中，直接返回
    if (loadingRef.current || isLoadingGlobal) {
      return;
    }

    // 如果不强制刷新且缓存有效，使用缓存
    if (!forceRefresh && isCacheValid()) {
      setConversations(globalConversationsCache);
      return;
    }

    try {
      loadingRef.current = true;
      isLoadingGlobal = true;
      setLoading(true);
      setError(null);
      
      // 取消之前的请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      // 创建新的AbortController
      abortControllerRef.current = new AbortController();
      
      console.log('[ConversationProvider] 开始加载对话列表...');

      // 使用API请求管理器，自动处理缓存和去重
      const data = await apiRequest.get<{ success: boolean; conversations: Conversation[]; error?: string }>(
        '/api/conversations',
        {
          cacheKey: 'conversations-list',
          cacheDuration: forceRefresh ? 0 : 30000, // 强制刷新时不使用缓存
          enableCache: !forceRefresh,
        }
      );
      
      if (data.success) {
        const conversationList = data.conversations || [];
        
        // 更新全局缓存
        globalConversationsCache = conversationList;
        globalCacheTimestamp = Date.now();
        
        setConversations(conversationList);
        console.log(`[ConversationProvider] 成功加载 ${conversationList.length} 个对话`);
      } else {
        setError(data.error || '加载对话列表失败');
      }
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('[ConversationProvider] 请求被取消');
        return;
      }
      setError('网络错误，加载对话列表失败');
      console.error('加载对话列表失败:', err);
    } finally {
      loadingRef.current = false;
      isLoadingGlobal = false;
      setLoading(false);
      abortControllerRef.current = null;
    }
  }, [isCacheValid]);

  // 创建新对话
  const createConversation = useCallback(async (title: string, model: string): Promise<number | null> => {
    try {
      setError(null);
      console.log(`[ConversationProvider] 开始创建对话: ${title}, 模型: ${model}`);
      
      const data = await apiRequest.post<{ success: boolean; conversation: Conversation; error?: string }>(
        '/api/conversations',
        { title, model },
        {
          cacheKey: `create-conversation-${title}-${model}`,
          enableCache: false, // 创建操作不使用缓存
        }
      );
      
      if (data.success) {
        console.log(`[ConversationProvider] 对话创建成功:`, data.conversation);
        
        // 刷新对话列表
        await loadConversations(true);
        
        // 切换到新创建的对话
        setCurrentConversation(data.conversation);
        
        return data.conversation.id;
      } else {
        console.error(`[ConversationProvider] 创建对话失败:`, data.error);
        setError(data.error || '创建对话失败');
        return null;
      }
    } catch (err) {
      console.error(`[ConversationProvider] 创建对话异常:`, err);
      setError('网络错误，创建对话失败');
      return null;
    }
  }, [loadConversations]);

  // 切换对话
  const switchConversation = useCallback(async (id: number) => {
    try {
      setError(null);
      console.log(`[ConversationProvider] 开始切换到对话 ${id}`);
      
      const data = await apiRequest.get<{ success: boolean; conversation: Conversation; error?: string }>(
        `/api/conversations/${id}`,
        {
          cacheKey: `conversation-${id}`,
          cacheDuration: 10000, // 对话详情缓存10秒
        }
      );
      
      if (data.success) {
        console.log(`[ConversationProvider] 成功获取对话 ${id} 信息:`, data.conversation);
        setCurrentConversation(data.conversation);
      } else {
        console.error(`[ConversationProvider] 切换对话 ${id} 失败:`, data.error);
        setError(data.error || '切换对话失败');
        throw new Error(data.error || '切换对话失败');
      }
    } catch (err) {
      console.error(`[ConversationProvider] 切换对话 ${id} 异常:`, err);
      setError('网络错误，切换对话失败');
      throw err;
    }
  }, []);

  // 删除对话
  const deleteConversation = useCallback(async (id: number) => {
    try {
      setError(null);
      console.log(`[ConversationProvider] 开始删除对话 ${id}`);
      
      const data = await apiRequest.delete<{ success: boolean; error?: string }>(
        `/api/conversations/${id}`,
        {
          cacheKey: `delete-conversation-${id}`,
          enableCache: false, // 删除操作不使用缓存
        }
      );
      
      if (data.success) {
        console.log(`[ConversationProvider] 对话 ${id} 删除成功`);
        
        // 从本地状态中移除
        setConversations(prev => prev.filter(conv => conv.id !== id));
        
        // 更新全局缓存
        globalConversationsCache = globalConversationsCache.filter(conv => conv.id !== id);
        
        // 如果删除的是当前对话，清空当前对话
        if (currentConversation?.id === id) {
          setCurrentConversation(null);
        }
      } else {
        console.error(`[ConversationProvider] 删除对话 ${id} 失败:`, data.error);
        setError(data.error || '删除对话失败');
        throw new Error(data.error || '删除对话失败');
      }
    } catch (err) {
      console.error(`[ConversationProvider] 删除对话 ${id} 异常:`, err);
      setError('网络错误，删除对话失败');
      throw err;
    }
  }, [currentConversation?.id]);

  // 更新对话标题
  const updateConversationTitle = useCallback(async (id: number, title: string) => {
    try {
      setError(null);
      console.log(`[ConversationProvider] 开始更新对话 ${id} 标题: ${title}`);
      
      const data = await apiRequest.patch<{ success: boolean; error?: string }>(
        `/api/conversations/${id}`,
        { title },
        {
          cacheKey: `update-conversation-${id}`,
          enableCache: false, // 更新操作不使用缓存
        }
      );
      
      if (data.success) {
        console.log(`[ConversationProvider] 对话 ${id} 标题更新成功`);
        
        // 更新本地状态
        setConversations(prev => 
          prev.map(conv => 
            conv.id === id ? { ...conv, title } : conv
          )
        );
        
        // 更新全局缓存
        globalConversationsCache = globalConversationsCache.map(conv => 
          conv.id === id ? { ...conv, title } : conv
        );
        
        // 如果是当前对话，也更新当前对话状态
        if (currentConversation?.id === id) {
          setCurrentConversation(prev => prev ? { ...prev, title } : null);
        }
      } else {
        console.error(`[ConversationProvider] 更新对话 ${id} 标题失败:`, data.error);
        setError(data.error || '更新对话标题失败');
        throw new Error(data.error || '更新对话标题失败');
      }
    } catch (err) {
      console.error(`[ConversationProvider] 更新对话 ${id} 标题异常:`, err);
      setError('网络错误，更新对话标题失败');
      throw err;
    }
  }, [currentConversation?.id]);

  // 初始化时加载对话列表
  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  // 组件卸载时清理 - 优化：清理所有请求和缓存
  useEffect(() => {
    return () => {
      console.log('[ConversationProvider] 组件卸载，清理资源...');

      // 取消当前的AbortController
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // 取消所有API请求管理器中的请求
      apiRequestManager.cancelAllRequests();

      // 重置全局状态
      isLoadingGlobal = false;
    };
  }, []);

  const value: ConversationContextType = {
    conversations,
    currentConversation,
    loading,
    error,
    loadConversations,
    createConversation,
    switchConversation,
    deleteConversation,
    updateConversationTitle,
    setCurrentConversation,
  };

  return (
    <ConversationContext.Provider value={value}>
      {children}
    </ConversationContext.Provider>
  );
}

export function useConversations() {
  const context = useContext(ConversationContext);
  if (context === undefined) {
    throw new Error('useConversations must be used within a ConversationProvider');
  }
  return context;
}
