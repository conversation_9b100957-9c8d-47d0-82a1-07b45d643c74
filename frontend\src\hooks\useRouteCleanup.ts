'use client';

import { useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { apiRequestManager } from '@/utils/apiRequestManager';

/**
 * 路由清理Hook - 在页面切换时清理资源
 */
export function useRouteCleanup() {
  const pathname = usePathname();
  const previousPathnameRef = useRef<string | null>(null);
  const cleanupCallbacksRef = useRef<(() => void)[]>([]);

  // 注册清理回调
  const registerCleanup = (callback: () => void) => {
    cleanupCallbacksRef.current.push(callback);
    
    // 返回取消注册的函数
    return () => {
      const index = cleanupCallbacksRef.current.indexOf(callback);
      if (index > -1) {
        cleanupCallbacksRef.current.splice(index, 1);
      }
    };
  };

  // 执行清理
  const executeCleanup = () => {
    console.log('[useRouteCleanup] 执行路由清理...');
    
    // 执行所有注册的清理回调
    cleanupCallbacksRef.current.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('[useRouteCleanup] 清理回调执行失败:', error);
      }
    });
    
    // 清理API请求管理器
    apiRequestManager.cancelAllRequests();
    
    // 清理过期缓存
    apiRequestManager.clearExpiredCache();
  };

  // 监听路由变化
  useEffect(() => {
    // 如果不是第一次加载且路由发生了变化
    if (previousPathnameRef.current !== null && previousPathnameRef.current !== pathname) {
      console.log(`[useRouteCleanup] 路由从 ${previousPathnameRef.current} 切换到 ${pathname}`);
      executeCleanup();
    }
    
    previousPathnameRef.current = pathname;
  }, [pathname]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      executeCleanup();
      cleanupCallbacksRef.current = [];
    };
  }, []);

  return {
    registerCleanup,
    executeCleanup,
    currentPath: pathname,
  };
}

/**
 * 页面级别的路由清理Hook
 * 用于页面组件注册特定的清理逻辑
 */
export function usePageCleanup(cleanupFn?: () => void) {
  const { registerCleanup } = useRouteCleanup();
  
  useEffect(() => {
    if (cleanupFn) {
      const unregister = registerCleanup(cleanupFn);
      return unregister;
    }
  }, [cleanupFn, registerCleanup]);
}

/**
 * API请求清理Hook
 * 用于组件级别的API请求管理
 */
export function useApiCleanup() {
  const abortControllersRef = useRef<AbortController[]>([]);
  
  // 创建新的AbortController
  const createAbortController = () => {
    const controller = new AbortController();
    abortControllersRef.current.push(controller);
    return controller;
  };
  
  // 取消所有请求
  const cancelAllRequests = () => {
    abortControllersRef.current.forEach(controller => {
      if (!controller.signal.aborted) {
        controller.abort();
      }
    });
    abortControllersRef.current = [];
  };
  
  // 移除已完成的AbortController
  const removeController = (controller: AbortController) => {
    const index = abortControllersRef.current.indexOf(controller);
    if (index > -1) {
      abortControllersRef.current.splice(index, 1);
    }
  };
  
  // 组件卸载时清理
  useEffect(() => {
    return () => {
      cancelAllRequests();
    };
  }, []);
  
  return {
    createAbortController,
    cancelAllRequests,
    removeController,
  };
}
