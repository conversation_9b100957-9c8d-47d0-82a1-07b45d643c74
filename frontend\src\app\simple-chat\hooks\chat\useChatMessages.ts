'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { OllamaModel } from '../../types';
import { AgentWithRelations } from '@/app/agents/types';
import { apiRequest } from '@/utils/apiRequestManager';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'tool_call';
  content: string;
  timestamp: number;
  isThinking?: boolean;
  tool_calls?: any[];
  toolCall?: ToolCall;
}

interface ToolCall {
  id: string;
  toolName: string;
  args: any;
  status: 'executing' | 'completed' | 'error';
  result?: string;
  error?: string;
  startTime: number;
  executionTime?: number;
}

// 本地存储键名
const SELECTED_MODEL_KEY = 'chat_selected_model';
const CONVERSATION_MODEL_KEY_PREFIX = 'chat_conversation_model_';

export function useChatMessages() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [selectedModel, setSelectedModel] = useState('');
  const [models, setModels] = useState<OllamaModel[]>([]);
  const [expandedThinkingMessages, setExpandedThinkingMessages] = useState<Set<string>>(new Set());
  
  // Agent-related state
  const [selectedAgent, setSelectedAgent] = useState<AgentWithRelations | null>(null);
  const [systemPrompt, setSystemPrompt] = useState<string | null>(null);
  
  // 工具相关状态
  const [enableTools, setEnableTools] = useState(false);
  const [selectedTools, setSelectedTools] = useState<string[]>([]);
  const [activeTool, setActiveTool] = useState<ToolCall | null>(null);
  const [toolCalls, setToolCalls] = useState<ToolCall[]>([]);
  const [currentAssistantMessageId, setCurrentAssistantMessageId] = useState<string | null>(null);
  
  // 添加AbortController来控制请求中断
  const [abortController, setAbortController] = useState<AbortController | null>(null);

  // 防止重复加载模型的标志
  const modelsLoadedRef = useRef(false);

  // --- New function to handle agent selection ---
  const selectAgent = useCallback(async (agentId: number | null) => {
    if (agentId === null) {
      setSelectedAgent(null);
      setSystemPrompt(null);
      // Optionally reset other settings or leave them as they were
      return;
    }

    try {
      const response = await fetch(`/api/agents/${agentId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch agent details');
      }
      const agent: AgentWithRelations = await response.json();
      setSelectedAgent(agent);
      
      // Override chat settings with agent's configuration
      setSelectedModel(agent.model.base_model);
      setEnableTools(agent.tools.length > 0);
      setSelectedTools(agent.tools.map(t => t.name));
      if (agent.system_prompt) {
        setSystemPrompt(agent.system_prompt);
      } else {
        setSystemPrompt(null);
      }
      console.log(`Agent "${agent.name}" selected. Model set to "${agent.model.base_model}".`);
    } catch (error) {
      console.error('Error selecting agent:', error);
      // Handle error, maybe show a notification to the user
      setSelectedAgent(null); // Reset on error
    }
  }, []);

  // 从本地存储加载已保存的模型选择
  const loadSavedModel = useCallback(() => {
    try {
      const savedModel = localStorage.getItem(SELECTED_MODEL_KEY);
      return savedModel;
    } catch (error) {
      console.warn('无法从localStorage读取保存的模型:', error);
      return null;
    }
  }, []);

  // 从本地存储加载特定对话的模型选择
  const loadConversationModel = useCallback((conversationId: number) => {
    try {
      const key = `${CONVERSATION_MODEL_KEY_PREFIX}${conversationId}`;
      const savedModel = localStorage.getItem(key);
      return savedModel;
    } catch (error) {
      console.warn('无法从localStorage读取对话模型:', error);
      return null;
    }
  }, []);

  // 保存模型选择到本地存储 - 修复：移除useCallback依赖，使其稳定
  const saveModelSelection = useCallback((modelName: string, conversationId?: number) => {
    try {
      // 保存全局模型选择
      localStorage.setItem(SELECTED_MODEL_KEY, modelName);
      
      // 如果有对话ID，也保存对话特定的模型选择
      if (conversationId) {
        const key = `${CONVERSATION_MODEL_KEY_PREFIX}${conversationId}`;
        localStorage.setItem(key, modelName);
        console.log(`保存对话 ${conversationId} 的模型选择: ${modelName}`);
      }
    } catch (error) {
      console.warn('无法保存模型选择到localStorage:', error);
    }
  }, []); // 空依赖数组，函数体内不依赖任何外部变量

  // 包装setSelectedModel以添加持久化 - 修复：现在依赖稳定了
  const setSelectedModelWithPersistence = useCallback((modelName: string, conversationId?: number) => {
    setSelectedModel(modelName);
    saveModelSelection(modelName, conversationId);
  }, [saveModelSelection]); // saveModelSelection现在是稳定的

  // 智能模型选择函数 - 修复：所有依赖现在都是稳定的
  const selectBestModel = useCallback((
    availableModels: OllamaModel[],
    conversationId?: number,
    lastUsedModel?: string,
    conversationModel?: string
  ) => {
    if (!availableModels.length) return;

    // 按优先级尝试不同的模型选择策略
    const strategies = [
      // 1. 对话中最后使用的模型
      lastUsedModel,
      // 2. 对话特定保存的模型
      conversationId ? loadConversationModel(conversationId) : null,
      // 3. 对话创建时的模型
      conversationModel,
      // 4. 全局保存的模型
      loadSavedModel(),
      // 5. 第一个可用模型
      availableModels[0]?.name
    ];

    for (const candidateModel of strategies) {
      if (candidateModel && availableModels.some(model => model.name === candidateModel)) {
        console.log(`选择模型: ${candidateModel} (策略: ${strategies.indexOf(candidateModel) + 1})`);
        setSelectedModel(candidateModel);
        // 保存选择
        saveModelSelection(candidateModel, conversationId);
        return candidateModel;
      }
    }
  }, [loadSavedModel, loadConversationModel, saveModelSelection]); // 现在所有依赖都是稳定的

  // 获取模型列表 - 优化：防止重复加载，使用API缓存
  useEffect(() => {
    // 如果已经加载过模型，跳过
    if (modelsLoadedRef.current) {
      return;
    }

    const fetchModels = async () => {
      try {
        console.log('[useChatMessages] 开始加载模型列表...');

        const data = await apiRequest.get<{ success: boolean; models: OllamaModel[]; error?: string }>(
          '/api/models',
          {
            cacheKey: 'ollama-models',
            cacheDuration: 60000, // 模型列表缓存1分钟
          }
        );

        if (data.success) {
          const fetchedModels = data.models || [];
          setModels(fetchedModels);
          modelsLoadedRef.current = true;

          console.log(`[useChatMessages] 成功加载 ${fetchedModels.length} 个模型`);

          if (fetchedModels.length > 0 && !selectedModel) {
            // Only select a model if one isn't already selected.
            selectBestModel(fetchedModels);
          }
        } else {
          console.error('获取模型失败:', data.error);
        }
      } catch (err) {
        console.error('获取模型失败:', err);
      }
    };

    fetchModels();
  }, []); // 只在组件挂载时运行一次

  // 思考面板切换
  const toggleThinkingExpand = useCallback((messageId: string) => {
    setExpandedThinkingMessages(prev => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(messageId)) {
        newExpanded.delete(messageId);
      } else {
        newExpanded.add(messageId);
      }
      return newExpanded;
    });
  }, []);

  // 停止生成
  const stopGeneration = useCallback(() => {
    // 中断正在进行的请求
    if (abortController) {
      console.log('🛑 中断正在进行的请求');
      abortController.abort();
      setAbortController(null);
    }
    
    // 重置流式状态
    setIsStreaming(false);
  }, [abortController]);

  return {
    // 消息状态
    messages,
    setMessages,
    inputMessage,
    setInputMessage,
    isStreaming,
    setIsStreaming,
    selectedModel,
    setSelectedModel: setSelectedModelWithPersistence, // 使用带持久化的版本
    models,
    expandedThinkingMessages,
    setExpandedThinkingMessages,
    
    // Agent state
    selectedAgent,
    systemPrompt,

    // 工具状态
    enableTools,
    setEnableTools,
    selectedTools,
    setSelectedTools,
    activeTool,
    setActiveTool,
    toolCalls,
    setToolCalls,
    currentAssistantMessageId,
    setCurrentAssistantMessageId,
    
    // 方法
    toggleThinkingExpand,
    stopGeneration,
    selectAgent, // Export the new function
    
    // AbortController
    abortController,
    setAbortController,
    
    // 新增的智能模型选择方法
    selectBestModel,
  };
} 